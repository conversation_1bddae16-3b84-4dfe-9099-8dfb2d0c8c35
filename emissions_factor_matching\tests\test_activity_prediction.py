from unittest import TestCase
from fastapi.testclient import TestClient
from utils import logger
from emissions_factor_matching.api import model_api
from utils import logger


real_world_validations = [
    ("non-ionic polyol", "non-ionic surfactant production, ethylene oxide derivate"),
    ("Lauramine Oxide", "non-ionic surfactant production, fatty acid derivate"),
    ("Amylase", "enzymes production"),
    ("Cellulase", "enzymes production"),
    ("Sodium Lauryl Sulphate", "fatty alcohol sulfate production, coconut oil"),
    ("Glycolic Acid", "lactic acid production"),
    ("Subtilisin", "enzymes production"),
    ("Cocamidopropylamine Oxide", "non-ionic surfactant production, fatty acid derivate"),
    ("Alkyl Polyglycoside", "non-ionic surfactant production, fatty acid derivate"),
    ("Alkylglucosid", "non-ionic surfactant production, fatty acid derivate"),
    ("Potassium Cocoate", "non-ionic surfactant production, fatty acid derivate"),
    ("2-Propanol, 1-(2-butoxy-1-methylethoxy)", "dipropylene glycol monomethyl ether production"),
    ("Carboxylic Copolymer", "acrylonitrile-butadiene-styrene copolymer production"),
    ("Benzalkonium Chloride", "benzyl chloride production"),
    ("Didecyldimonium Chloride", "alkyl sulphate (C12-14) production"),
    ("Sodium Laureth Sulfate", "alkyl sulphate (C12-14) production"),
    ("Peg 8 glyceryl isostearate", "stearic acid production"),
    ("Citric Acid", "citric acid production"),
    ("Alcohol Alkoxylate", "ethoxylated alcohol (AE3) production, coconut oil"),
    ("Deionised Water", "water production, deionised"),
    ("Tetrasodium Glutamate Diacetate", "citric acid production"),
    ("Fragrance", "dodecanol production, from coconut oil"),
    ("Vegetable extract", "vegetable oil refinery construction"),
    ("Lactic Acid", "lactic acid production"),
    ("Propylene Glycol", "propylene glycol production, liquid"),
    ("Poly(diallyldimethylammonium Chloride)", "polyaluminium chloride production"),
    ("Optical Brightening Agent", "optical brighteners production, for paper production"),
    ("Pectinase", "enzymes production"),
    ("Di (palm carboxyethyl) hyroxyethyl methyl ammonium methosulphate", "ethoxylated alcohol (AE3) production, palm kernel oil"),
    ("Salt", "sodium production, sodium chloride electrolysis, molten salt cell"),
    ("Vinylpyrrolidone/vinylimidazole copolymer", "styrene-acrylonitrile copolymer production"),
    ("Sorbic Acid", "propionic acid production"),
    ("Cocamidopropyl Betaine", "non-ionic surfactant production, fatty acid derivate"),
    ("Sodium Lauryl Sulphate", "fatty alcohol sulfate production, coconut oil"),
    ("Triethanolamine", "ethanolamine production"),
    ("Benzisothiazolinone", "benzimidazole-compound production"),
    ("Polypropylene", "polypropylene production, granulate"),
    ("Polyethylene, recycled", "polyethylene production, high density, granulate, recycled"),
    ("Polyethylene", "polyethylene production, high density, granulate"),
]

assert_correct = [
    "Polypropylene",
    "Triethanolamine",
    "Optical Brightening Agent",
    "Propylene Glycol",
    "Lactic Acid",
    "Deionised Water",
    "Citric Acid",
    "Alkyl Polyglycoside",
]

client = TestClient(model_api)

class TestEmissionsFactorMatchingAPI(TestCase):
    def test_real_world_validations(self):
        """Real World Benchmark"""
        correct = 0
        for chemical_name, validation_activity in real_world_validations:
            response = client.post(
                "/activities/recommendations",
                json={
                    "chemical_name": chemical_name,
                    "number_of_matches": 25,
                },
                headers={"api-version": "beta"}
            )

            match = response.json()["matched_activity"]
            correct_match = match["activity_name"] == validation_activity

            if correct_match:
                logger.success(f"'{validation_activity}' is the first result for: {chemical_name}")
                correct += 1
            elif chemical_name in assert_correct:
                self.fail(f"{chemical_name} is expected to pass. Expected: {validation_activity}")
            else:
                logger.warning(f"'{validation_activity}' not the first results for: {chemical_name}")

        if correct != len(real_world_validations):
            logger.warning(f"{correct} / {len(real_world_validations)} correct")
        else:
            logger.success("All Passing")
