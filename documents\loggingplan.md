# Emission Factor Matching: Logging Plan

This document outlines a plan to implement comprehensive logging throughout the emission factor matching pipeline to trace outputs at each stage of processing.

## Logging Objectives

1. Capture the input and output of each stage in the emission factor matching flow
2. Provide enough context to understand the transformation at each step
3. Use consistent log levels to differentiate between normal operation and exceptional cases
4. Enable easy correlation between related log entries using request IDs
5. Avoid excessive logging that could impact performance

## Proposed Logging Strategy

We'll use the existing `logger` from `utils.logger` and implement a structured logging approach with the following levels:

- `logger.debug()` - For detailed debugging information
- `logger.info()` - For tracking normal flow and key decision points
- `logger.warning()` - For unexpected but non-fatal conditions
- `logger.error()` - For errors that prevent successful matching

## Key Points for Logging Implementation

### 1. Backend API Handler (GraphQL Layer)

**File:** Not visible in the provided codebase, but would be in the GraphQL handler that calls `EmissionsFactorsService`

**Logging to add:**

```python
# At the start of the handler
logger.info(f"EF Matching request received: chemical_name={chemical_name}, geography={geography}, tenant_id={tenant_id}")

# After checking for overrides
if override_ef:
    logger.info(f"Found prediction override for '{chemical_name}': {override_ef.activity_name} ({override_ef.geography})")
```

### 2. Emissions Factor Service (Main Service)

**File:** Not visible in provided codebase, but would be in the `EmissionsFactorsService` class

**Logging to add:**

```python
# At service entry point
logger.info(f"Starting EF matching for '{ingredient_name}', override={emissions_factor_override is not None}")

# After curated match check
if curated_match:
    logger.info(f"Found curated match for '{ingredient_name}': {curated_match.activity_name}")
else:
    logger.info(f"No curated match found for '{ingredient_name}', proceeding to ML service")
```

### 3. ML Service API Endpoint

**File:** `emissions_factor_matching/api.py`

**Logging to add:**

```python
# Add request ID to correlate logs across the request lifecycle
@model_api.post("/activities/recommendations")
@cached(cache)
async def get_recommended_activities(
    activity_request: ActivityRecommendationsRequest,
    api_version: str = Depends(get_api_version),
):
    request_id = f"req-{uuid.uuid4().hex[:8]}"
    logger.info(f"[{request_id}] ML Service request: material='{activity_request.chemical_name}', iso={activity_request.iso_code or 'GLO'}")

    # Add more detailed logging throughout
```

### 4. Input Classification Stage

**File:** `emissions_factor_matching/predictions.py` (modify `predict_input_category` function)

**Logging to add:**

```python
def predict_input_category(input_str: str, request_id: str = None):
    log_prefix = f"[{request_id}]" if request_id else ""
    logger.info(f"{log_prefix} Classifying input: '{input_str}'")

    # After LLM call
    logger.info(f"{log_prefix} Input classified as: {category}")
    return category
```

### 5. Input Enrichment Stage

**File:** `emissions_factor_matching/api.py` (in `get_recommended_activities`)

**Logging to add:**

```python
# For products
logger.info(f"[{request_id}] Product: '{material}', constituent: '{constituent}'")
logger.info(f"[{request_id}] Enriched query: '{query_text}'")

# For chemicals
logger.info(f"[{request_id}] Chemical: '{material}', common names: '{common_names}'")
logger.info(f"[{request_id}] Product description: '{description}'")
logger.info(f"[{request_id}] Enriched query: '{query_text}'")
```

### 6. Filter Construction Stage

**File:** `emissions_factor_matching/api.py` (in `get_recommended_activities`)

**Logging to add:**

```python
# After ISIC section prediction
if product_section:
    logger.info(f"[{request_id}] ISIC section filter applied: {product_section}")

# After constructing all filters
logger.info(f"[{request_id}] Final query filters: {json.dumps(where)}")
```

### 7. ChromaDB Vector Search Stage

**File:** `emissions_factor_matching/api.py` (in `get_recommended_activities`)

**Logging to add:**

```python
# Before ChromaDB query
logger.info(f"[{request_id}] Executing vector search with query: '{query_text}'")

# After ChromaDB query
logger.info(f"[{request_id}] Vector search returned {len(documents['ids'][0])} results")
logger.debug(f"[{request_id}] Top 5 vector search results: {documents['documents'][0][:5]}")

# Optional: Log distances for top results (if available)
if 'distances' in documents:
    logger.debug(f"[{request_id}] Top 5 similarity scores: {documents['distances'][0][:5]}")
```

### 8. LLM Re-Ranking Stage

**File:** `emissions_factor_matching/predictions.py` (modify `get_closest_match` function)

**Logging to add:**

```python
async def get_closest_match(product_name: str, activities: Dict, request_id: str = None) -> Dict | None:
    log_prefix = f"[{request_id}]" if request_id else ""
    logger.info(f"{log_prefix} Re-ranking {len(activities)} candidates for '{product_name}'")

    # After LLM completion
    logger.info(f"{log_prefix} Selected match: {completion_dict['activity_uuid']} with confidence: {completion_dict['confidence']}")
    logger.info(f"{log_prefix} Match explanation: {completion_dict['match_explanation']}")

    return completion_dict
```

### 9. Geography Matching Stage

**File:** `emissions_factor_matching/geography.py` (modify `get_geography_activity_match` function)

**Logging to add:**

```python
def get_geography_activity_match(activity_name: str, iso_code: str, reference_product_name: str, request_id: str = None):
    log_prefix = f"[{request_id}]" if request_id else ""
    logger.info(f"{log_prefix} Finding geography match for '{activity_name}', target geography: {iso_code}")

    # After geography resolution
    logger.info(f"{log_prefix} Resolved geography: {resolved_geography} for activity: '{activity_name}'")

    return activity
```

### 10. Response Assembly Stage

**File:** `emissions_factor_matching/api.py` (in `get_recommended_activities`)

**Logging to add:**

```python
# Before returning response
logger.info(f"[{request_id}] Final matched activity: '{matched_activity.activity_name}' ({matched_activity.geography})")
logger.info(f"[{request_id}] Returning {len(recommendations)} alternative recommendations")
```

### 11. Technical Quality Assessment (Optional)

**File:** `emissions_factor_matching/predictions.py` (modify `get_technological_representation` function)

**Logging to add:**

```python
async def get_technological_representation(process_name: str, activity_name: str, request_id: str = None):
    log_prefix = f"[{request_id}]" if request_id else ""
    logger.info(f"{log_prefix} Evaluating technological representation: '{process_name}' vs '{activity_name}'")

    # After comparison
    logger.info(f"{log_prefix} Technology match results: Process design: {process_design_comparison['similar']}, " +
               f"Operating conditions: {operating_condition_comparison['similar']}, " +
               f"Material quality: {material_quality_comparison['similar']}, " +
               f"Process scale: {process_scale_comparison['similar']}")
```

## Implementation Approach

To minimize code changes while maximizing visibility into the process:

1. Start by implementing logging in the main flow in `emissions_factor_matching/api.py`
2. Add request ID correlation through the chain of function calls
3. Add detailed logging in the helper functions in other modules
4. Consider adding a log configuration option to control verbosity

## Log Output Format

For maximum readability and parsability, logs should follow a consistent format:

```
[timestamp] [log_level] [request_id] [stage] message
```

Example:

```
[2025-05-22 14:32:45] [INFO] [req-a1b2c3d4] [INPUT_CLASSIFICATION] Input classified as: PRODUCT
```

## Viewing and Analyzing Logs

Logs are currently stored in the `logs/` directory with daily rotation. To analyze the logs:

1. Grep for a specific request ID to follow the complete flow
2. Use log aggregation tools to analyze patterns and performance
3. Consider implementing a simple log viewer script to highlight the flow of a specific request

## Next Steps

1. Review this plan and prioritize which sections to implement first
2. Implement logging in the main API file
3. Gradually extend to other modules as needed
4. Run tests to verify logs capture the complete flow
5. Create simple log analysis scripts if needed
