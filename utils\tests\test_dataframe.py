from unittest import TestCase
import tempfile
import os
import pandas as pd
from utils.dataframe import get_dataframe


class TestDataframe(TestCase):
    """
    Test dataframe utils - get_dataframe
    """
    @classmethod
    def setUpClass(cls) -> None:
        cls.temp_dir = tempfile.TemporaryDirectory()
        cls.test_df = pd.DataFrame({
            "a": [1, 4],
            "b": [2, 5],
            "c": [3, 6]
        })

        cls.csv_file_path = os.path.join(cls.temp_dir.name, "test.csv")

        with open(cls.csv_file_path, "w", encoding="utf-8") as f:
            cls.test_df.to_csv(f, index=False)

        cls.tsv_file_path = os.path.join(cls.temp_dir.name, "test.tsv")

        with open(cls.tsv_file_path, "w", encoding="utf-8") as f:
            cls.test_df.to_csv(f, sep="\t", index=False)

        cls.excel_file_path = os.path.join(cls.temp_dir.name, "test.xlsx")

        with open(cls.excel_file_path, "wb") as f:
            cls.test_df.to_excel(f, index=False)

    @classmethod
    def tearDownClass(cls) -> None:
        cls.temp_dir.cleanup()

    def test_get_dataframe(self):
        csv_df = get_dataframe(self.csv_file_path, "text/csv")
        for i, row in csv_df.iterrows():
            for j, val in row.items():
                self.assertEqual(val, self.test_df.iloc[i][j])

        tsv_df = get_dataframe(self.tsv_file_path, "text/tab-separated-values")
        for i, row in tsv_df.iterrows():
            for j, val in row.items():
                self.assertEqual(val, self.test_df.iloc[i][j])

        excel_df = get_dataframe(self.excel_file_path, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        for i, row in excel_df.iterrows():
            for j, val in row.items():
                self.assertEqual(val, self.test_df.iloc[i][j])
