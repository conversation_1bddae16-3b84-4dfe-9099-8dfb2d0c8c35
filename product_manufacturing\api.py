from fastapi import FastAPI
from product_manufacturing.predictions import predict_manufacturing_processes
from utils.cache import create_cache, cached

model_api = FastAPI()
cache = create_cache(prefix='pm_')

@model_api.get("/manufacturing-processes/{product_category}/{product_name}")
@cached(cache)
def get_manufacturing_processes(product_category: str, product_name: str):
    return predict_manufacturing_processes(product_category, product_name)
