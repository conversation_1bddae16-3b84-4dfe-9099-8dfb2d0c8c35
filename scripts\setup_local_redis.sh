#!/bin/bash

CONTAINER_NAME="ml-model-redis"
REDIS_TAG="6"
REDIS_PORT="6379"
REDIS_PASSWORD=""

check_docker() {
    docker info >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "Docker daemon not running, starting Docker..."
        sudo systemctl start docker
        sleep 5
        docker info >/dev/null 2>&1
        if [ $? -ne 0 ]; then
            echo "Failed to start Docker."
            exit 1
        fi
    fi
    echo "Docker is running."
}

check_docker

if [ "$(docker ps -aq -f name=^${CONTAINER_NAME}$)" ]; then
    echo "Container $CONTAINER_NAME exists. Removing it..."
    docker stop $CONTAINER_NAME
    docker rm -f $CONTAINER_NAME
fi

echo "Starting Redis container..."
docker run --name $CONTAINER_NAME \
    -p $REDIS_PORT:6379 \
    -v $(pwd)/scripts/config/redis.conf:/usr/local/etc/redis/redis.conf \
    -d redis:$REDIS_TAG \
    redis-server /usr/local/etc/redis/redis.conf

# Wait for Redis to start
until docker exec $CONTAINER_NAME redis-cli ping > /dev/null 2>&1; do
    echo "Waiting for Redis to start..."
    sleep 2
done

echo "Redis is ready!"