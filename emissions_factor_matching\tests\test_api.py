from unittest import TestCase
import sys
__import__('pysqlite3')
sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')

from fastapi.testclient import TestClient
from utils import logger
from emissions_factor_matching.api import model_api

geography_match_validations = [
    ("market for electricity, medium voltage", "electricity, medium voltage", "US", "US"),
    ("market for sodium bicarbonate", "sodium bicarbonate", "GB", "RER"),
    ("1,1-difluoroethane production", "1,1-difluoroethane", "GB", "RoW"),
]

class TestAPI(TestCase):
    client = TestClient(model_api)
    
    def setUp(self):
        """Clear cache before each test to ensure we test the actual implementation"""
        response = self.client.post("/cache/clear")
        # Don't fail if cache clear fails (for environments without cache)
        logger.info(f"Cache clear response: {response.status_code}")

    def test_smoke_test_search_activities(self):
        response = self.client.get(
            "/search",
            params={
                "query": "non-ionic polyol",
                "geography_iso3": "GLO",
                "number_of_results": 50
            }
        )
        logger.info(response.json())
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 50)

    def test_smoke_test_activity_recommendations(self):
        response = self.client.post(
            "/activities/recommendations",
            json={"chemicalName": "non-ionic polyol"},
        )
        logger.info(response.json())
        self.assertEqual(response.status_code, 200)

    def test_smoke_test_activity_recommendations_with_geography(self):
        response = self.client.post(
            "/activities/recommendations",
            json={"chemicalName": "non-ionic polyol", "geography": "DE"},
        )
        logger.info(response.json())
        self.assertEqual(response.status_code, 200)

    def test_smoke_test_activity_recommendations_with_product_category(self):
        response = self.client.post(
            "/activities/recommendations",
            json={"chemicalName": "adjuster", "productCategory": "Backpacks"},
        )
        logger.info(response.json())
        self.assertEqual(response.status_code, 200)
        
    def test_backward_compatibility_chemical_name(self):
        """Test that old parameter names still work"""
        response = self.client.post(
            "/activities/recommendations",
            json={"chemical_name": "steel", "iso_code": "US", "product_category": "construction"},
        )
        logger.info(response.json())
        self.assertEqual(response.status_code, 200)

    def test_geography_match(self):
        for (
            activity_name,
            reference_product_name,
            target_geography_iso,
            expected_geography
        ) in geography_match_validations:
            response = self.client.get(
                "/geography-match",
                params={
                    "activity_name": activity_name,
                    "reference_product_name": reference_product_name,
                    "target_geography_iso": target_geography_iso
                }
            )

            data = response.json()
            self.assertTrue(expected_geography in data["geography"].split("-"))

    def test_with_lca_lifecycle_stage(self):
        """Test new lcaLifecycleStage parameter"""
        body = {
            "chemicalName": "Steel",
            "geography": "USA",
            "lcaLifecycleStage": "Raw Materials"
        }

        response = self.client.post(
            "/activities/recommendations",
            json=body,
        )

        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertIn("matched_activity", data)
        self.assertIn("confidence", data)
        self.assertIn("explanation", data)
        self.assertIn("recommendations", data)
