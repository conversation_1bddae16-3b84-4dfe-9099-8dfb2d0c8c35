import torch
from torch import nn
from torchvision.models import resnet50, ResNet50_Weights
from training.data_loaders import dataset

resnet50 = resnet50(weights=ResNet50_Weights.IMAGENET1K_V2)

for param in resnet50.parameters():
    param.requires_grad = False

num_features = resnet50.fc.in_features

resnet50.fc = nn.Linear(num_features, len(dataset.classes))

loss_function = nn.CrossEntropyLoss()
optimizer = torch.optim.SGD(resnet50.fc.parameters(), lr=0.001, momentum=0.9)

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

resnet50 = resnet50.to(device)
loss_function = loss_function.to(device)