import os
import zipfile
import pandas as pd
import numpy as np
import time
from typing import List, Dict, Any, Optional
from torch import tensor
from huggingface_hub import hf_hub_download, login
import chromadb
from emissions_factor_matching.model import embedding_function, eol_embedding_function, Candidate
from config import config
from utils import logger
login(token=config.hf_token)

chroma_db_zip = hf_hub_download(
    repo_id="CarbonBright/emissions-factors-activities-overview",
    filename="chroma_with_instructor_embeddings_v2.zip",
    repo_type="dataset",
)

DOCUMENT_DIR = "/tmp/chroma_documents"
COLLECTION_NAME = "emission_activities"
DOCUMENT_DIR_EOL = "/tmp/chroma_documents_eol"
COLLECTION_NAME_EOL = "eol_activities"

os.makedirs(DOCUMENT_DIR, exist_ok=True)
os.makedirs(DOCUMENT_DIR_EOL, exist_ok=True)

with zipfile.ZipFile(chroma_db_zip, 'r') as zip_ref:
    zip_ref.extractall(DOCUMENT_DIR)

client = chromadb.PersistentClient(path=DOCUMENT_DIR + "/chroma_db")
collection = client.get_collection(COLLECTION_NAME, embedding_function=embedding_function)

chroma_db_eol_zip = hf_hub_download(
    repo_id="CarbonBright/emissions-factors-activities-overview",
    filename="chroma_with_instructor_embeddings_eol.zip",
    repo_type="dataset",
)

with zipfile.ZipFile(chroma_db_eol_zip, 'r') as zip_ref:
    zip_ref.extractall(DOCUMENT_DIR_EOL)

client_eol = chromadb.PersistentClient(path=DOCUMENT_DIR_EOL + "/chroma_db")
collection_eol = client_eol.get_collection(COLLECTION_NAME_EOL, embedding_function=eol_embedding_function)

efs_with_geographies_filepath = hf_hub_download(
    repo_id="CarbonBright/emissions-factors-activities-overview",
    filename="emissions_factors_with_geographies_v2.pkl",
    repo_type="dataset",
)

efs_with_geographies = pd.read_pickle(efs_with_geographies_filepath)


def search_candidates(augmented_query: str, filters: Dict[str, Any], n_results: int = 25) -> List[Candidate]:
    """
    Phase 1.6: ChromaDB Vector Search Enhancement

    Performs enhanced vector search using the augmented query from Phase 1.4
    and dynamic filters from Phase 1.5 to find the most relevant emission factor candidates.

    Args:
        augmented_query: Enhanced query text from Phase 1.4 (e.g., "Heavy-duty diesel freight transport truck >32 tonnes long-haul road transportation logistics")
        filters: Dynamic filters from Phase 1.5 (e.g., {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_4_code": {"$eq": "4923"}}]})
        n_results: Number of candidates to return (default: 25)

    Returns:
        List[Candidate]: List of candidate emission factors with metadata and similarity scores
    """
    log_prefix = "phase_6_search"
    logger.info(f"{log_prefix} Executing ChromaDB vector search")
    logger.info(f"{log_prefix} Query: '{augmented_query}'")
    logger.info(f"{log_prefix} Filters: {filters}")
    logger.info(f"{log_prefix} Requesting {n_results} results")

    start_time = time.time()

    try:
        # Execute ChromaDB query with enhanced parameters
        results = collection.query(
            query_texts=[augmented_query],
            where=filters,
            n_results=n_results,
            include=['metadatas', 'documents', 'distances']
        )

        elapsed_time = (time.time() - start_time) * 1000

        # Extract results
        ids = results.get('ids', [[]])[0]
        documents = results.get('documents', [[]])[0]
        metadatas = results.get('metadatas', [[]])[0]
        distances = results.get('distances', [[]])[0]

        num_results = len(ids)
        logger.info(f"{log_prefix} ChromaDB search completed in {elapsed_time:.2f}ms")
        logger.info(f"{log_prefix} Retrieved {num_results} candidates")

        if num_results == 0:
            logger.warning(f"{log_prefix} No candidates found for query")
            return []

        # Convert ChromaDB results to Candidate objects
        candidates = []
        for i in range(num_results):
            try:
                metadata = metadatas[i] if i < len(metadatas) else {}
                distance = distances[i] if i < len(distances) else 1.0

                # Extract ISIC code from ChromaDB format "4923:Freight transport by road"
                isic_classification = metadata.get('ISIC Classification', '')
                isic_4_code = None
                if isinstance(isic_classification, str) and ':' in isic_classification:
                    isic_4_code = isic_classification.split(':')[0].strip()
                elif isinstance(isic_classification, (int, str)) and str(isic_classification).strip():
                    isic_4_code = str(isic_classification).strip()

                # Ensure we have a valid ISIC code
                if not isic_4_code or isic_4_code == '':
                    isic_4_code = None

                # Create Candidate object with all available metadata
                candidate = Candidate(
                    # Core identifiers
                    activity_uuid=metadata.get('uuid', ids[i]),
                    chroma_id=ids[i],

                    # Activity information
                    activity_name=documents[i] if i < len(documents) else "Unknown Activity",
                    reference_product_name=metadata.get('reference_product_name', ''),
                    product_information=metadata.get('product_information'),

                    # Source and classification
                    source=metadata.get('source', 'Unknown'),
                    isic_4_code=isic_4_code,  # Use extracted numeric code
                    isic_section=metadata.get('isic_section'),
                    activity_type=metadata.get('activity_type', 'ordinary transforming activity'),

                    # Geography and units
                    geography=metadata.get('geography'),
                    unit=metadata.get('unit'),

                    # Search relevance
                    distance=distance,
                    similarity_score=max(0.0, 1.0 - distance),

                    # Additional metadata
                    metadata=metadata
                )

                candidates.append(candidate)

                # Log top candidates for debugging
                if i < 3:  # Log first 3 candidates
                    logger.info(f"{log_prefix} Candidate {i+1}: '{candidate.activity_name}' (distance: {distance:.4f}, similarity: {candidate.similarity_score:.4f})")

            except Exception as e:
                logger.error(f"{log_prefix} Error creating candidate {i}: {str(e)}")
                continue

        # Sort candidates by distance (ascending = most similar first)
        candidates.sort(key=lambda x: x.distance)

        logger.info(f"{log_prefix} Successfully created {len(candidates)} candidate objects")
        logger.info(f"{log_prefix} Best match: '{candidates[0].activity_name}' (distance: {candidates[0].distance:.4f})")

        return candidates

    except Exception as e:
        elapsed_time = (time.time() - start_time) * 1000
        logger.error(f"{log_prefix} ChromaDB search failed in {elapsed_time:.2f}ms: {str(e)}")

        # Return empty list on error
        return []


def search_candidates_with_fallback(augmented_query: str, filters: Dict[str, Any], n_results: int = 25) -> List[Candidate]:
    """
    Enhanced search with fallback strategy for better reliability

    Attempts the primary search with full filters, then falls back to simpler filters
    if no results are found, ensuring we always return some candidates when possible.
    """
    log_prefix = "phase_6_fallback"

    # Try primary search first
    candidates = search_candidates(augmented_query, filters, n_results)

    if candidates:
        logger.info(f"{log_prefix} Primary search successful with {len(candidates)} candidates")
        return candidates

    # Fallback 1: Try with just activity_type filter
    logger.warning(f"{log_prefix} Primary search returned no results, trying fallback with basic filters")

    basic_filters = {"activity_type": {"$eq": "ordinary transforming activity"}}
    candidates = search_candidates(augmented_query, basic_filters, n_results)

    if candidates:
        logger.info(f"{log_prefix} Fallback search successful with {len(candidates)} candidates")
        return candidates

    # Fallback 2: Try with no filters (last resort)
    logger.warning(f"{log_prefix} Fallback search returned no results, trying without filters")

    candidates = search_candidates(augmented_query, {}, n_results)

    if candidates:
        logger.info(f"{log_prefix} No-filter search successful with {len(candidates)} candidates")
    else:
        logger.error(f"{log_prefix} All search strategies failed - no candidates found")

    return candidates
