import os
import json
import tempfile
import time
import shutil
import asyncio
from unittest import TestCase
from pydantic import BaseModel
from utils.cache import BaseCache, DiskCache, cached, make_serializable, serialize_args_kwargs

class TestModel(BaseModel):
    name: str
    value: int

class TestBaseCache(TestCase):
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.cache = DiskCache(cache_dir=self.temp_dir)

    def tearDown(self):
        self.cache.clear()
        shutil.rmtree(self.temp_dir)

    def test_make_serializable(self):
        # Test with Pydantic model
        model = TestModel(name="test", value=42)
        serialized = make_serializable(model)
        self.assertEqual(serialized, {"name": "test", "value": 42})

        # Test with nested Pydantic models
        nested = {"model": model, "list": [model]}
        serialized = make_serializable(nested)
        expected = {
            "model": {"name": "test", "value": 42},
            "list": [{"name": "test", "value": 42}]
        }
        self.assertEqual(serialized, expected)

    def test_serialize_args_kwargs(self):
        model = TestModel(name="test", value=42)
        args = (1, 2, model)
        kwargs = {"a": model, "b": [model]}

        serialized = serialize_args_kwargs(args, kwargs)
        expected = {
            "args": [1, 2, {"name": "test", "value": 42}],
            "kwargs": {
                "a": {"name": "test", "value": 42},
                "b": [{"name": "test", "value": 42}]
            }
        }
        self.assertEqual(serialized, json.dumps(expected, sort_keys=True))

    def test_cache_basic_operations(self):
        # Test set and get
        self.cache.set("key1", "value1")
        self.assertEqual(self.cache.get("key1"), "value1")

        # Test default value
        self.assertIsNone(self.cache.get("nonexistent"))
        self.assertEqual(self.cache.get("nonexistent", "default"), "default")

        # Test expiration
        self.cache.set("key2", "value2", expire=1)
        self.assertEqual(self.cache.get("key2"), "value2")
        time.sleep(1.1)
        self.assertIsNone(self.cache.get("key2"))

    def test_cache_clear(self):
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        self.assertEqual(len(self.cache), 2)

        self.cache.clear()
        self.assertEqual(len(self.cache), 0)
        self.assertIsNone(self.cache.get("key1"))
        self.assertIsNone(self.cache.get("key2"))

    def test_cache_volume(self):
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        volume = self.cache.volume()
        self.assertGreater(volume, 0)

    def test_cached_decorator(self):
        @cached(self.cache)
        async def test_func(a, b):
            return a + b

        # Run async function and get result
        result = asyncio.run(test_func(1, 2))
        self.assertEqual(result, 3)

        # Second call should use cache
        result = asyncio.run(test_func(1, 2))
        self.assertEqual(result, 3)

        # Different args should compute again
        result = asyncio.run(test_func(2, 3))
        self.assertEqual(result, 5)

    def test_cached_decorator_with_pydantic(self):
        @cached(self.cache)
        async def test_func(model: TestModel):
            return model.value * 2

        model = TestModel(name="test", value=42)

        # First call should compute
        result = asyncio.run(test_func(model))
        self.assertEqual(result, 84)

        # Second call should use cache
        result = asyncio.run(test_func(model))
        self.assertEqual(result, 84)

        # Different model should compute again
        model2 = TestModel(name="test2", value=21)
        result = asyncio.run(test_func(model2))
        self.assertEqual(result, 42)

    def test_cached_decorator_empty_results(self):
        call_count = 0

        @cached(self.cache)
        async def test_func(return_type):
            nonlocal call_count
            call_count += 1

            if return_type == "none":
                return None
            elif return_type == "empty_string":
                return ""
            elif return_type == "empty_list":
                return []
            elif return_type == "empty_dict":
                return {}
            return "not empty"

        # Test each type of empty result
        empty_types = ["none", "empty_string", "empty_list", "empty_dict"]
        for empty_type in empty_types:
            initial_count = call_count

            # First call
            result1 = asyncio.run(test_func(empty_type))
            self.assertEqual(call_count, initial_count + 1)

            # Second call - should compute again (not use cache)
            result2 = asyncio.run(test_func(empty_type))
            self.assertEqual(call_count, initial_count + 2)

            # Results should be equal
            self.assertEqual(result1, result2)

        # Test non-empty result is cached
        initial_count = call_count

        # First call
        result1 = asyncio.run(test_func("not_empty"))
        self.assertEqual(call_count, initial_count + 1)

        # Second call should use cache
        result2 = asyncio.run(test_func("not_empty"))
        self.assertEqual(call_count, initial_count + 1)

        self.assertEqual(result1, "not empty")
        self.assertEqual(result2, "not empty")

    def test_cached_decorator_refresh_expiry(self):
        """Test that refresh_expiry functionality works correctly with proper integer values."""
        from utils.cache import RedisCache, create_cache

        # Skip this test if Redis is not available
        try:
            redis_cache = create_cache('redis')
            if not isinstance(redis_cache, RedisCache):
                self.skipTest("Redis cache not available, skipping refresh_expiry test")
        except Exception:
            self.skipTest("Redis cache not available, skipping refresh_expiry test")

        call_count = 0

        @cached(redis_cache, expire=5, refresh_expiry=True)  # 5 second expiry, refresh enabled
        async def test_func_with_refresh():
            nonlocal call_count
            call_count += 1
            return {"data": "test_refresh", "call_count": call_count}

        @cached(redis_cache, expire=5, refresh_expiry=False)  # 5 second expiry, refresh disabled
        async def test_func_without_refresh():
            nonlocal call_count
            call_count += 1
            return {"data": "test_no_refresh", "call_count": call_count}

        # Test with refresh_expiry=True
        initial_count = call_count

        # First call - cache miss
        result1 = asyncio.run(test_func_with_refresh())
        self.assertEqual(call_count, initial_count + 1)
        self.assertEqual(result1["data"], "test_refresh")

        # Second call - cache hit with refresh_expiry
        result2 = asyncio.run(test_func_with_refresh())
        self.assertEqual(call_count, initial_count + 1)  # Should not increment (cache hit)
        self.assertEqual(result2["data"], "test_refresh")
        self.assertEqual(result1["call_count"], result2["call_count"])  # Same cached result

        # Test with refresh_expiry=False
        initial_count = call_count

        # First call - cache miss
        result3 = asyncio.run(test_func_without_refresh())
        self.assertEqual(call_count, initial_count + 1)
        self.assertEqual(result3["data"], "test_no_refresh")

        # Second call - cache hit without refresh_expiry
        result4 = asyncio.run(test_func_without_refresh())
        self.assertEqual(call_count, initial_count + 1)  # Should not increment (cache hit)
        self.assertEqual(result4["data"], "test_no_refresh")
        self.assertEqual(result3["call_count"], result4["call_count"])  # Same cached result

    def test_redis_cache_refresh_expiry_parameter_types(self):
        """Test that RedisCache.get() properly handles refresh_expiry parameter types."""
        from utils.cache import RedisCache, create_cache

        # Skip this test if Redis is not available
        try:
            redis_cache = create_cache('redis')
            if not isinstance(redis_cache, RedisCache):
                self.skipTest("Redis cache not available, skipping parameter type test")
        except Exception:
            self.skipTest("Redis cache not available, skipping parameter type test")

        # Test data
        test_key = "test_refresh_expiry_types"
        test_data = {"message": "testing refresh_expiry parameter types"}

        # Set initial data with expiry
        redis_cache.set(test_key, test_data, expire=60)

        # Test 1: refresh_expiry=None (should not refresh)
        result1 = redis_cache.get(test_key, refresh_expiry=None)
        self.assertEqual(result1, test_data)

        # Test 2: refresh_expiry=300 (should refresh with 300 seconds)
        result2 = redis_cache.get(test_key, refresh_expiry=300)
        self.assertEqual(result2, test_data)

        # Test 3: refresh_expiry=0 (should work - 0 is a valid expiry meaning immediate expiration)
        result3 = redis_cache.get(test_key, refresh_expiry=0)
        self.assertEqual(result3, test_data)

        # Clean up
        redis_cache.redis_client.delete(redis_cache._prefixed_key(test_key))

    def test_cached_decorator_passes_correct_refresh_expiry_type(self):
        """
        Test that verifies the fix for the boolean bug - the cached decorator should pass
        the integer expire value to refresh_expiry parameter, not a boolean.

        This test documents the bug we found and fixed where the parameter was named
        'refresh_expiry' but was actually receiving integer values.
        """
        from utils.cache import RedisCache, create_cache

        # Skip this test if Redis is not available
        try:
            redis_cache = create_cache('redis')
            if not isinstance(redis_cache, RedisCache):
                self.skipTest("Redis cache not available, skipping refresh_expiry type test")
        except Exception:
            self.skipTest("Redis cache not available, skipping refresh_expiry type test")

        # Track what values are actually passed to the get method
        original_get = redis_cache.get
        captured_refresh_expiry_values = []

        def mock_get(key, default=None, refresh_expiry=None):
            captured_refresh_expiry_values.append(refresh_expiry)
            return original_get(key, default, refresh_expiry)

        redis_cache.get = mock_get

        try:
            @cached(redis_cache, expire=123, refresh_expiry=True)
            async def test_func():
                return {"test": "data"}

            # First call - cache miss (refresh_expiry not used)
            asyncio.run(test_func())

            # Second call - cache hit (refresh_expiry should be used)
            asyncio.run(test_func())

            # Verify that the refresh_expiry parameter received the integer expire value (123)
            # not the boolean refresh_expiry flag (True)
            self.assertEqual(len(captured_refresh_expiry_values), 2)

            # Both calls should receive the integer value 123 because the cached decorator
            # always passes the expire value when refresh_expiry=True and cache is RedisCache
            self.assertEqual(captured_refresh_expiry_values[0], 123)  # First call gets expire value
            self.assertEqual(captured_refresh_expiry_values[1], 123)  # Second call gets expire value
            self.assertIsInstance(captured_refresh_expiry_values[0], int)  # Verify it's an integer
            self.assertIsInstance(captured_refresh_expiry_values[1], int)  # Verify it's an integer

            # The key point: we're receiving integers (123), not booleans (True)

        finally:
            # Restore original method
            redis_cache.get = original_get