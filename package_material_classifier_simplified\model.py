import torch
from huggingface_hub import hf_hub_download
from torchvision.models import resnet50

REPO_ID = "CarbonBright/packaging-base-material-classification"
FILENAME = "packaging_classifier_64_epochs_weights.pth"

weights_path = hf_hub_download(repo_id=REPO_ID, filename=FILENAME)

model = resnet50()
num_ftrs = model.fc.in_features
model.fc = torch.nn.Linear(num_ftrs, 5)

if torch.cuda.is_available():
    device = torch.device("cuda")
else:
    device = torch.device("cpu")

model.load_state_dict(
    torch.load(
        weights_path,
        map_location=device,
    ),
)

model.eval()