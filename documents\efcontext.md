Document Part 1: Introduction, Problem, Goals, Rationale
Advanced AI-Assisted Emission Factor Matching: A Context-Driven Approach (Phase 1)
(This document outlines the design for Phase 1. An Executive Summary can be drafted once the full content is finalized.)
1. The Challenge: Enhancing Our Current EF Matching Capabilities & Phased Approach
Our current Emission Factor (EF) matching system, while providing a foundational level of automation for Life Cycle Assessment (LCA), has demonstrated limitations in its ability to deeply understand contextual nuances for all lifecycle stages. Its reliance on broader input categorizations (such as "Product" or "Chemical") and a more static filtering logic can sometimes lead to EF suggestions that are not optimally aligned with the practitioner's specific intent, particularly for complex material definitions, manufacturing processes, and nuanced transportation scenarios. This has been observed to necessitate manual review and overrides, impacting practitioner efficiency and the consistency of EF selection.

Furthermore, a distinct and simplified handling mechanism currently exists for End-of-Life (EoL) EFs, operating separately from the primary AI-driven matching logic.

This proposal outlines a significantly enhanced, context-driven AI matching system. Phase 1 of this initiative will focus on revolutionizing EF matching for Material Acquisition (raw and processed materials), Manufacturing processes/inputs, and Transportation EFs. Enhancements and integration for End-of-Life EF matching into this new AI framework are planned for a subsequent Phase 2.

Evidence from User Override Data & Current System Limitations: Analysis of user interactions and current system behaviors starkly illustrates existing limitations:

Misinterpretation of Material Intent (Targeted by New AI System - Phase 1): For a query describing a polyurethane precursor ("1,4-Butanediol, polymer with..."), our previous simpler matching logic might suggest an EF for the basic chemical ("1,4-butanediol production"). Users, however, correctly select an EF for the actual processed material ("polyurethane production, flexible foam..."). This highlights a failure to understand that the query describes a component leading to a specific polymer, a gap Phase 1 of the new system will address.

Limited Transportation EF Selection (Targeted by New AI System - Phase 1): While users can select transport EFs in some parts of the application (e.g., Process Model page), the underlying matching logic is often limited. A query explicitly including "Transportation Segment 1" (".25 Fender Washer bundle Transportation Segment 1") when processed by a simpler system (biased by "Product" classification and manufacturing filters) could result in irrelevant suggestions like machinery production (e.g., "cable yarder production"). Phase 1 of the new system will provide intelligent, context-aware matching for transportation EFs, moving beyond inflexible defaults or limited search capabilities.

Separate & Simplified End-of-Life EF Processing (Current State; Phase 2 Target for New AI System): EoL EF selection currently utilizes a separate, simpler system (/eol/activity endpoint, collection_eol database) that bypasses the main AI matching pipeline. While dedicated, its logic is less sophisticated. An EoL query mistakenly routed through the main EF matching system might result in an irrelevant suggestion. Phase 2 will explore unifying and enhancing EoL matching under the new AI framework.

The examples for materials, manufacturing, and transportation highlight the current system's inability to grasp user intent beyond literal keyword matching for these in-scope Phase 1 items. The proposed context-driven system aims to resolve these issues, building a robust foundation for incorporating EoL in Phase 2.
2. Goals & Objectives of the Revised System
The primary goals for this advanced AI-assisted EF matching system (across all planned phases, with Phase 1 focusing on Materials, Manufacturing, and Transport) are:

Accuracy: To select the most scientifically accurate and relevant emission factor for any given input, reflecting its true environmental impact within the specific context of the LCA.
Efficiency: To drastically reduce the time and effort LCA practitioners spend on finding and selecting EFs, allowing them to focus on higher-value analysis and interpretation.
Consistency & Standardization: To promote more consistent and standardized EF selection across projects and practitioners by embedding expert logic and best practices into the system.
Transparency: To provide clear justifications for recommended EFs, making the selection process transparent and auditable.
Usability for Non-Experts (to a degree): While designed with LCA practitioners in mind, a more intelligent system can also guide less experienced users toward better choices, though expert oversight remains crucial.
Continuous Improvement: To create a system that learns and improves over time (as detailed in Phase 7 of the workflow), becoming more adept at matching and potentially identifying gaps in EF data.
3. Rationale & Guiding Principles for the Context-Driven Approach
The design of this revised EF matching system is rooted in several core principles:

LCA is Context-Dependent: The fundamental principle of LCA is that environmental impacts are highly dependent on the specific context: what material/process is involved, how it's produced/used, where it happens, and its role within the product's lifecycle. The system's design mirrors this by prioritizing the lca_lifecycle_stage and detailed attributes of an input.
Mirroring Expert Thinking: The phased approach (Setting Stage, Understanding Need, Query & Filter Formulation, Matching & Recommendation, Learning) mimics how an experienced LCA practitioner would mentally (or manually) approach the task.
Leveraging AI Strengths:
LLMs for Natural Language Understanding (NLU): Parsing user queries, recognizing entities and attributes, understanding intent, augmenting queries, and generating explanations.
Vector Databases (ChromaDB): For efficient semantic search over large EF datasets.
Structured Data & Rules: While AI provides flexibility, the system will also leverage structured metadata (like ISIC classifications) and can incorporate expert rules where beneficial.
Iterative Refinement for Precision: The process acknowledges that initial user inputs are often imperfect and builds in mechanisms (like modifier spotting and contextual query augmentation) to achieve the necessary specificity.
Data Quality Matters: Recognizing that an LCA is only as good as its input data, the system emphasizes finding high-quality, recent, and geographically relevant EFs from reliable databases.
Transparency Builds Trust: For a system making critical choices like EF selection, users (especially experts) need to understand why a particular EF was recommended. Justification, as provided by the LLM re-ranker, is key for adoption and correct use.
Beyond Simplistic Categorization: The historical "Product vs. Chemical" distinction has proven insufficient for nuanced EF selection. The real determinant of an EF isn't such a broad category but its specific nature, attributes, and role within a defined lifecycle stage.

You are absolutely right, and I apologize for that oversight in my previous response. My intention was to provide the updated content, but I see how "This section remains as per the last approved version..." is not helpful when you need a complete, self-contained document for easy integration.

I will ensure that for the remaining parts, and if we do any further revisions, I provide the full text of every section, even if parts of it were unchanged from a very recent iteration. The goal is a final document you can use directly.

Document Part 2 (Corrected - with full Phase 7): Proposed Solution - Architecture & Detailed Workflow
4. Proposed Solution: The Context-Driven EF Matching System (Phase 1 Focus)
To address the identified limitations for material, manufacturing, and transportation EF selection, and to build a scalable foundation for future enhancements, we propose a new, context-driven EF matching system. This redesigned system will intelligently interpret user inputs by systematically incorporating LCA lifecycle context and leveraging advanced AI capabilities. Phase 1 will concentrate on Material Acquisition, Manufacturing, and Transportation lifecycle stages.

4.1. System Architecture Overview

Current Architecture:


flowchart TD
    subgraph UserInput["1. User Input"]
        A[User provides:\\nchemical_name: eco-friendly laundry detergent\\niso_code: USA\\nvalid_units: kg\\nproduct_category: Laundry Supplies]
    end

    subgraph InitialProcessing["2. Initial Processing & Enrichment"]
        B1[CAS Number Attempt\\nvia LLM call\\nResult: NONE]
        B2[Input Category Prediction\\nvia LLM call\\nResult: PRODUCT]
        B3[Product Enrichment]
        B4[Query Text Formation]
        
        B3 --> B3a[Append product category:\\neco-friendly laundry detergent for Laundry Supplies]
        B3 --> B3b[Predict constituents via LLM call:\\nsurfactants, enzymes, water]
        B3a & B3b --> B4
        B4 --> B4a[Final Query Text:\\nsurfactants, enzymes, water eco-friendly laundry detergent for Laundry Supplies]
    end

    subgraph FilterConstruction["3. Filter Construction"]
        C1[Base Filter:\\nactivity_type: ordinary transforming activity]
        C2[ISIC Section Prediction via LLM call\\ne.g., C - Manufacturing]
        C3[Add Unit Filter: kg]
        C4[Complete Filter]
        
        C1 & C2 & C3 --> C4
    end

    subgraph VectorDBSearch["4. Vector DB Search"]
        D1[Embed Query Text]
        D2[Query ChromaDB with\\nembedding and filters]
        D3[Retrieve top N candidates\\ne.g., detergent production, powder,\\nsurfactant production, anionic, etc.]
        
        D1 --> D2 --> D3
    end

    subgraph LLMReranking["5. LLM Re-ranking"]
        E1[Send Query Text and\\nN candidates to LLM]
        E2[LLM selects best match:\\ne.g., detergent production, powder]
        E3[LLM provides confidence: medium]
        E4[LLM provides explanation]
        
        E1 --> E2 --> E3 --> E4
    end

    subgraph GeographyMatching["6. Geography Matching & Record Retrieval"]
        F1[Use activity_name, reference_product_name\\nand iso_code: USA]
        F2[Query efs_with_geographies DataFrame]
        F3[Find best geographical variant\\ne.g., detergent production, powder, RNA]
        F4[Process other N-1 candidates similarly]
        
        F1 --> F2 --> F3
        F1 --> F4
    end

    subgraph Output["7. Output"]
        G1[matched_activity: Full record for\\ndetergent production, powder, RNA]
        G2[confidence: medium]
        G3[explanation: From LLM]
        G4[recommendations: List of other\\ngeographically matched EF records]
    end

    A --> B1 & B2
    B1 & B2 --> B3
    B4a --> D1
    C4 --> D2
    D3 --> E1
    E2 & E4 --> F1
    F3 & F4 --> G1 & G2 & G3 & G4


The proposed system comprises several key modules that work in concert to process user requests and deliver accurate EF matches:

flowchart TD
    %% Initial User Input
    Start[/"User Input (Phase 1 Focus):
    - chemical_name: '.25 Fender Washer bundle Transportation Segment 1'
    - lca_lifecycle_stage: 'TRANSPORT'
    - iso_code: 'USA'
    - valid_units: ['tkm']
    - product_category: 'Industrial Components'"/]
    
    %% Step 1: User Input & Initial Context Processing
    subgraph "Phase 1: Input & Context Processing"
        InputCat["Input Category Prediction<br/>(Enhanced LLM call)"]
        ModSpot["Modifier Spotting<br/>(LLM call)"]
        ISICPredict["LLM/Logic: ISIC Class<br/>Prediction/Derivation"] 
    end
    
    %% Step 2: Query & Filter Formulation
    subgraph "Phase 2: Query & Filter Formulation"
        QueryAug["Query Text Augmentation<br/>(LLM call)"]
        FilterConst["Dynamic Filter Construction<br/>(Logic incorporating Predicted Input Type,<br/>Modifiers & Predicted ISIC Class)"]
    end
    
    %% Step 3: Vector DB Search (ChromaDB)
    subgraph "Phase 3: Vector DB Search (ChromaDB)"
        EmbedQuery["Embed Augmented Query"]
        ChromaQuery["Query ChromaDB with<br/>Augmented Query & Dynamic Filters"]
    end
    
    %% Step 4: LLM Re-ranking
    subgraph "Phase 4: LLM Re-ranking & Justification"
        BuildPrompt["Construct Enhanced Prompt<br/>(Original Context + Candidates + System Understanding)"]
        LLMCall["Execute LLM Re-ranking Call"]
        ParseResp["Parse LLM Response<br/>(Selected Match, Confidence, Explanation)"]
    end
    
    %% Step 5: Geography Matching & Final Record Retrieval
    subgraph "Phase 5: Geography Matching & Record Retrieval"
        PrimMatch["Process Primary LLM Match<br/>(Find best geo-variant)"]
        AltMatch["Process Alternative Candidates<br/>(Find geo-variants)"]
    end
    
    %% Step 6: Output to User
    FinalOutput["Phase 6: Final Output to User<br/>(API Response Construction)"]
    
    %% Learning & Improvement Loop
    LearnLoop(("Phase 7: Learning & Improvement<br/>(Evaluation & Iterative Refinement<br/>based on Golden Dataset & Feedback)"))

    %% End
    End[/"API Response:
    - matched_activity (with UUID, name, etc.)
    - confidence level
    - explanation
    - recommendations"/]
    
    %% Flow Connections
    Start --> InputCat
    Start --> ModSpot 
    
    InputCat -- "input_type_predicted" --> ISICPredict
    ModSpot -- "detected_modifiers" --> ISICPredict
    Start -- "chemical_name, lca_lifecycle_stage" --> ISICPredict

    InputCat -- "input_type_predicted" --> QueryAug 
    ModSpot -- "detected_modifiers" --> QueryAug
    Start -- "chemical_name, lca_lifecycle_stage" --> QueryAug

    InputCat -- "input_type_predicted" --> FilterConst
    ModSpot -- "detected_modifiers" --> FilterConst
    ISICPredict -- "predicted_isic_class" --> FilterConst
    Start -- "iso_code, valid_units" --> FilterConst

    QueryAug -- "augmented_query_text" --> EmbedQuery
    FilterConst -- "complete_where_clause" --> ChromaQuery
    
    EmbedQuery -- "query_embedding" --> ChromaQuery
    ChromaQuery -- "top_n_candidates_from_db" --> BuildPrompt
    ChromaQuery -- "top_n_candidates_from_db" --> AltMatch

    Start -- "Original Request Context" --> BuildPrompt
    ModSpot -- "detected_modifiers" --> BuildPrompt
    InputCat -- "input_type_predicted" --> BuildPrompt
    ISICPredict -- "predicted_isic_class" --> BuildPrompt
    QueryAug -- "augmented_query_text" --> BuildPrompt

    BuildPrompt --> LLMCall
    LLMCall --> ParseResp
    ParseResp -- "selected_activity_uuid, confidence, explanation" --> PrimMatch
    
    PrimMatch -- "final_matched_ef_record" --> FinalOutput
    AltMatch -- "recommendations_list" --> FinalOutput
    
    FinalOutput --> End
    FinalOutput -- "User Interaction /<br/>System Performance Data<br/>(Forms Golden Dataset)" --> LearnLoop

    LearnLoop -.-> InputCat
    LearnLoop -.-> ModSpot
    LearnLoop -.-> ISICPredict
    LearnLoop -.-> QueryAug
    LearnLoop -.-> FilterConst
    LearnLoop -.-> BuildPrompt
    LearnLoop -.-> LLMCall

    classDef process fill:#f9f,stroke:#333,stroke-width:2px;
    classDef data fill:#bbf,stroke:#333,stroke-width:1px;
    classDef feedback_loop fill:#ffa500,stroke:#333,stroke-width:2px; 
    class Start,End data;
    class InputCat,ModSpot,ISICPredict,QueryAug,FilterConst,EmbedQuery,ChromaQuery,BuildPrompt,LLMCall,ParseResp,PrimMatch,AltMatch,FinalOutput process;
    class LearnLoop feedback_loop; 

Module Descriptions: (Examples implicitly refer to Phase 1 scope, including Transport where applicable.)

User Input Interface: Captures the user's primary query (chemical_name) and crucial contextual parameters: lca_lifecycle_stage, iso_code, valid_units, and product_category.
Context & Query Pre-processing Engine: Orchestrates initial understanding.
LLM: Enhanced Input Category Prediction: Classifies the user's input into fine-grained, LCA-relevant categories (e.g., PROCESSED_MATERIAL, PROCESS_ACTIVITY_MANUFACTURING, SERVICE_TRANSPORT_ROAD_FREIGHT), guided by lca_lifecycle_stage.
NLU: Modifier Spotting: Extracts key descriptive terms (e.g., "recycled," "powder coated," "lorry >32t," "diesel") from the user's query.
LLM/Logic: ISIC Class Prediction/Derivation: Based on the predicted custom LCA category, modifiers, and query, this step predicts or derives the most relevant ISIC Class code(s) (e.g., "2592" for coating of metals, "4923" for freight transport by road) to aid in precise filtering.
Query & Filter Formulation Module: Prepares inputs for the database search.
LLM: Query Text Augmentation: Generates a descriptive, context-aware query string for semantic search (e.g., for ".25 Fender Washer bundle Transportation Segment 1" in 'TRANSPORT' stage, this might become "road freight transport service for .25 fender washer bundle, segment 1").
Dynamic Filter Construction: Creates specific database filters based on lca_lifecycle_stage, predicted input category, predicted ISIC Class, geography, units, etc.
Vector DB Search Module (ChromaDB): Executes a semantic search using the augmented query text and dynamic filters.
LLM Re-ranking & Justification Module: Evaluates candidates to select the best match, provides confidence, and generates an explanation.
Geography Matching & Final Record Retrieval Module: Finds the best geographical variant.
Output Module: Constructs and delivers the final API response.
Learning & Model Improvement Loop: Facilitates continuous system enhancement.

4.2. Detailed Workflow (Phase 1 Focus: Materials, Manufacturing Processes & Transportation):

The system processes inputs through the following key phases. For Phase 1, lca_lifecycle_stage values processed by this AI pipeline will primarily be those related to material sourcing, manufacturing, and transportation (e.g., 'RawMaterialAcquisition', 'Manufacturing', 'Transport'). EndOfLife stages will utilize the existing separate system and are targeted for integration into this AI framework in Phase 2.

PHASE 0: Setting the Stage (Upfront Context)

Input: lca_lifecycle_stage (e.g., 'TRANSPORT'), iso_code ('USA'), product_category ('Industrial Components'), chemical_name (e.g., ".25 Fender Washer bundle Transportation Segment 1"), valid_units (['tkm']).
System Priming: If lca_lifecycle_stage is 'TRANSPORT', subsequent steps are primed to identify transportation EFs (services, fuels). For Phase 1, if lca_lifecycle_stage is 'EndOfLifeTreatment', the request might be routed to the existing EoL system or return a "Phase 2" scope message.

PHASE 1: Understanding the User's Specific Need (Input & Context Processing)

The chemical_name (user query, e.g., ".25 Fender Washer bundle Transportation Segment 1") is analyzed.
Step 1A: Enhanced Input Category Prediction: An LLM, using the query and lca_lifecycle_stage: 'TRANSPORT', predicts category SERVICE_TRANSPORT_ROAD_FREIGHT (assuming "Segment 1" often implies road).
Step 1B: Modifier Spotting: LLM extracts:
goods_description: ".25 Fender Washer bundle"
process_indicator: "Transportation Segment 1"
transport_mode_hint (if parsable or based on rules for "Segment 1"): "road freight" or "lorry/truck"
Step 1C: ISIC Class Prediction/Derivation: Based on SERVICE_TRANSPORT_ROAD_FREIGHT, might predict ISIC Class "4923" (Freight transport by road).

PHASE 2: Query & Filter Formulation (Contextual & Dynamic)

Step 2A: Query Text Augmentation: For the transport example, this might become "road freight transport service for .25 fender washer bundle, first leg, using heavy lorry."
Step 2B: Dynamic Filter Construction: Based on lca_lifecycle_stage: 'TRANSPORT', category SERVICE_TRANSPORT_ROAD_FREIGHT, and ISIC "4923", filters might target:
activity_type IN ['transport activity', 'freight transport service'].
ISIC Classification LIKE "4923:%".
unit IN ['tkm', 'ton-km'].
Potentially keywords from modifiers if applicable (e.g., if "diesel" or vehicle size class were extracted and exist as metadata tags).

PHASE 3: Vector DB Search (ChromaDB Integration - Targeted Candidate Retrieval)

Input to ChromaDB: Augmented query "road freight transport service for .25 fender washer bundle, first leg, using heavy lorry" and filters derived in 2B.
Conceptual Result: Candidates including various lorry transport EFs, e.g., "transport, freight, lorry, >32 metric ton, diesel, EURO 6 (RoW)", "transport, freight, lorry, 16-32t...".

PHASE 4: LLM Re-ranking & Justification (Holistic Evaluation)

The LLM evaluates candidates. For the ".25 Fender Washer bundle..." query, it might select "transport, freight, lorry, >32 metric ton, diesel, EURO 6".
Justification: "This EF represents heavy road freight transport, suitable for a 'bundle' of industrial components on its 'Segment 1' (typically road-based). It aligns with the 'TRANSPORT' lifecycle stage and ISIC class '4923' for road freight."

PHASE 5: Geography Matching & Final Record Retrieval

Retrieves the best geographical variant for the selected lorry EF (e.g., a North American or USA-specific EF if available, otherwise RoW).

PHASE 6: Final Output to User (API Response Construction)

API response includes the matched lorry EF, confidence ("high"), and system reasoning (e.g., "Detected road freight transport intent for industrial components. Filtered by ISIC for road freight and unit 'tkm'. Selected best match for heavy goods lorry transport.").

PHASE 7: Learning & Improvement (Evaluation & Iterative Refinement Cycle) To ensure the system evolves and its accuracy is continuously validated and improved, a robust evaluation and iterative refinement cycle is critical. Initially, this will focus on manual tuning based on performance against a "golden dataset" derived from user interactions, with potential for more automated model fine-tuning in the future.

Data Collection for Evaluation & Analysis:
User Selections & Overrides (Golden Data Creation): When a user accepts a system recommendation, chooses an alternative, or (in the context of comparing with the previous system's behavior) performs an override, these interactions (input query, system suggestion(s), user's final selection, lca_lifecycle_stage, etc.) are logged. This systematically builds a "golden dataset" representing correct input-output pairings in specific contexts. This dataset becomes the primary benchmark for evaluating system performance.
Performance Monitoring: Tracking key metrics such as:
Match rate against the golden dataset.
Override rates (if users can still correct the new system, though ideally much lower).
User acceptance of top suggestions.
Qualitative Feedback Channels (Future): Eventually, incorporating mechanisms for users to provide direct, structured feedback on match quality.
System Evaluation & Manual Tuning (Iterative Refinement):
Regular Evaluation: The system's output for new or challenging queries will be regularly compared against the growing golden dataset.
Root Cause Analysis of Discrepancies: When the system's suggestion deviates from the "golden" user choice, a detailed analysis will be performed to understand the cause. This could involve reviewing:
The "Enhanced Input Category Prediction."
The effectiveness of "Modifier Spotting."
The "Query Text Augmentation" strategy.
The "Dynamic Filter Construction" logic.
The LLM prompts used in "Input Category Prediction," "Query Augmentation," and "Re-ranking & Justification."
Iterative Architectural & Prompt Adjustments: Based on this analysis, targeted, initially manual adjustments will be made to the system. This includes:
Prompt Engineering: Refining the prompts for the various LLM calls (Category Prediction, Augmentation, Re-ranking) to elicit more accurate and contextually appropriate responses. This is a primary tuning lever.
Heuristic Adjustments: Modifying any rules or weighting used in modifier spotting, ISIC prediction, filter construction, or query augmentation.
Logic Refinement: Small changes to the workflow logic if certain patterns of failure emerge.
The impact of these adjustments will be re-evaluated against the golden dataset.
Future: Potential for Model Fine-Tuning:
As the golden dataset grows significantly and matures, it could serve as training/validation data for fine-tuning the LLMs used within the system (for Input Category Prediction, Query Augmentation, Re-ranking). This would represent a more advanced stage of the learning loop, potentially leading to further accuracy gains and automation of the improvement process. This is a longer-term consideration.
Benefits of this Iterative Refinement Cycle:
Targeted Improvements: Focuses effort on known areas of weakness identified through real user data.
Agile Development: Allows for quicker, incremental improvements to system performance without the immediate overhead of full LLM retraining.
Measurable Progress: Performance against the golden dataset provides a clear metric for success.
Foundation for Future Automation: Establishes the data and processes necessary for more automated learning techniques later on.
This iterative cycle of collecting data, evaluating performance, analyzing deviations, and manually tuning the system (primarily through prompt engineering and logic adjustments) is key to progressively enhancing the system's intelligence and reliability.
Document Part 3: Addressing Limitations, Alternatives, Conclusion
5. Addressing Previous System Limitations (Phase 1 Focus & Phase 2 Outlook)
The proposed Context-Driven EF Matching System (Phase 1) directly resolves critical shortcomings for material, manufacturing, and transportation EF matching. Limitations related to End-of-Life EF matching identified in the current ecosystem are targeted for resolution in Phase 2 by extending this new AI framework.

Feature Area
Previous System's Limitation (for In-Scope Items)
Revised System's Solution & Advantage (Phase 1: Materials, Mfg, Transport)
Impact on User Override Examples (Phase 1 Scope)
1. Contextual Input
Lacked explicit lca_lifecycle_stage for nuanced differentiation; Transport EFs often hardcoded defaults or matched via limited logic.
Mandatory lca_lifecycle_stage input primes the system for the correct domain (material, mfg process, or transport service).
For "Transportation Segment 1," lca_lifecycle_stage: 'TRANSPORT' ensures search for relevant transport EFs, moving beyond inflexible defaults or simplistic matches. For "A380 Aluminum Powder Coated," lca_lifecycle_stage: 'Manufacturing' steers towards a manufacturing process EF.
2. Query Understanding
Simplistic "Product/Chemical" classification; missed critical modifiers defining specific items, processes, or transport details.
Enhanced Input Category Prediction, Modifier Spotting, & ISIC Class Prediction allow detailed classification (e.g., PROCESSED_MATERIAL_METAL, SERVICE_TRANSPORT_ROAD_FREIGHT, ISIC "4923") and attribute extraction.
For "1,4-Butanediol..." (polyurethane), system infers PROCESSED_MATERIAL. For transport queries like ".25 Fender Washer bundle...", it can identify goods and transport mode hints. For "A380 Aluminum Powder Coated," "Powder Coated" is a key process modifier. Predicted ISIC guides to relevant sectors.
3. Filter Construction
Rigid, often inappropriate filters (e.g., default ISIC Manufacturing for non-manufacturing or transport queries).
Dynamic & Contextual Filter Construction tailors filters (activity_type, ISIC Classification) to stage, category, modifiers, and predicted ISIC Class.
Prevents manufacturing filters on transport queries. Enables specific filters like activity_type: 'transport activity' and ISIC Classification like "4923:%" for road freight, or "2592:%" for metal coating.
4. Search Query
Used raw/poorly enriched text; superficial matches.
Augmented Query Text (LLM-generated) is context-aware, descriptive, better reflecting true user intent for semantic search.
For a transport query, augmented text focuses on "freight transport service for [goods described]". For "A380 Aluminum Powder Coated," augmented query becomes "powder coating process on A380 aluminum parts."
5. LLM Re-ranking
Received limited context; nuanced choices difficult.
LLM Re-ranking gets full context (request, stage, type, augmented query, candidates, ISIC alignment), enabling holistic, context-aware selection and clear justification.
LLM understands why a specific lorry EF is best for a "Transportation Segment 1" query, or why "powder coating, aluminium sheet" fits the "A380 Aluminum Powder Coated" manufacturing query, referencing process type and ISIC alignment.
6. Overall Accuracy (Phase 1 Scope)
Prone to errors for complex materials, specific mfg. processes. Transport EFs were inflexible defaults or poorly matched.
Significantly improved accuracy & flexibility expected for materials, mfg, & transport EFs, leading to more defensible selections, reduced overrides, and increased practitioner trust.
System designed to correctly identify EFs chosen by users (e.g., polyurethane production, specific transport EFs, powder coating) or offer intelligent alternatives where defaults/poor matches were previously the only option.
7. End-of-Life Handling
EoL: Separate, simpler system. (Current State)
Phase 1 of New AI System: EoL out of scope. Existing EoL mechanism remains. Phase 2 Plan: Integrate & enhance EoL matching using this new AI framework.
Phase 1: No change to EoL handling. User override data for EoL (if an EoL query was misrouted to the old main system) highlights the opportunity for improvement in Phase 2 by applying this AI-driven contextual approach.


By embedding deep contextual understanding at each step, Phase 1 of the revised system is designed to transition from a tool requiring frequent expert correction or offering inflexible defaults, to a more reliable, intelligent assistant for core LCA components: materials, manufacturing, and transportation.
6. Alternatives Considered & Trade-offs
While the proposed AI-driven context-aware system offers significant advantages, other approaches were considered:

Alternative 1: Fully Manual Selection by Practitioners.

Description: LCA practitioners manually search EF databases (e.g., Ecoinvent), read documentation, and select EFs based on their expertise.
Advantages: Full control for the expert.
Disadvantages: Extremely time-consuming, high cognitive load, prone to inconsistency, requires deep and broad expertise for every input, difficult to scale.
Why AI approach is preferred: Drastically improves speed, consistency, and can guide users to relevant factors they might miss, reducing drudgery.

Alternative 2: Basic Keyword Search in EF Databases (as in simpler existing systems).

Description: User types a term, and the system returns EFs containing that word, perhaps with rudimentary filters.
Advantages: Faster than fully manual browsing.
Disadvantages: Still requires significant sifting by the user, lacks contextual understanding (lifecycle stage, specific process attributes), often returns too many irrelevant results or misses the best one if keywords don't perfectly align.
Why AI approach is preferred: The proposed AI system understands nuance, context (lifecycle stage, modifiers), and can disambiguate, leading to far more targeted and relevant results.

Alternative 3: Simple Rule-Based Systems.

Description: A system with pre-programmed rules, e.g., "IF input='steel' AND stage='materials' THEN suggest 'steel, primary production'."
Advantages: Better than basic keyword search, some level of automation.
Disadvantages: Brittle (hard to maintain rules for all cases), cannot handle variations or novel inputs well, struggles with ambiguity, difficult to capture the vast complexity of all possible LCA scenarios.
Why AI approach is preferred: AI (especially LLMs and ML) offers greater flexibility, can understand natural language, learn from data (via the feedback loop), and handle ambiguity and novelty much more effectively.

Trade-offs of the Proposed AI System (Phase 1 and beyond):

Initial Development Complexity & Cost: Designing, building, and testing the multi-stage AI pipeline (NLU, LLM calls for various tasks, dynamic filtering logic) is more complex and resource-intensive than simpler approaches.
Data Dependency: Its effectiveness relies on well-structured, comprehensive EF databases with rich and accurate metadata (including the ISIC Classification which we've confirmed is available).
LLM Operational Costs & Latency: Each LLM call incurs operational costs and adds to processing latency. System design must optimize the number and efficiency of LLM interactions.
Prompt Engineering & Iteration: Achieving optimal performance from LLMs requires careful prompt engineering and iterative refinement based on evaluation against the "golden dataset" (as outlined in Phase 7). This is an ongoing effort.
Managing "Black Box" Risk: While LLMs can be powerful, their decision-making can sometimes be opaque. The emphasis on the "LLM Re-ranking & Justification" module providing clear explanations for its choices is crucial to mitigate this and build user trust.

Despite these trade-offs, the potential for significantly improving the accuracy, efficiency, and reliability of EF selection—core to credible LCAs—justifies the investment in this advanced AI-driven approach.
7. Conclusion
The proposed Context-Driven Emission Factor Matching System represents a significant leap forward from previous EF matching methodologies. By systematically incorporating LCA lifecycle stage as a primary contextual input, leveraging advanced AI for nuanced natural language understanding, dynamically constructing precise search filters (including detailed ISIC classifications), and employing LLMs for intelligent re-ranking and justification, this system is designed to deliver highly accurate and relevant EF recommendations.

Phase 1 will focus on establishing this robust framework for Material Acquisition, Manufacturing Process, and Transportation EFs, addressing key limitations and inflexibilities observed in current approaches for these critical areas. The modular architecture and iterative refinement cycle will ensure the system not only meets immediate needs but also evolves and improves over time. This initiative promises to enhance practitioner efficiency, improve the consistency and reliability of LCAs, and ultimately provide more trustworthy environmental impact assessments. The foundation laid in Phase 1 will pave the way for extending these advanced AI capabilities to other lifecycle stages, such as End-of-Life, in subsequent phases.

Okay, here are the updated Technical Design Document (TDD) Addendum and the Implementation Approach sections, now fully reflecting that Transportation is IN SCOPE for Phase 1 of the new AI EF matching system, while EoL remains out of scope for this phase.

I have provided each as a complete section for easy copy-pasting.


Document Part 4: Technical Design Document Addendum
Technical Design Document Addendum: AI-Assisted EF Matching Service (Phase 1)
Version: 1.0 Date: [Current Date] Status: Draft

1. Introduction

This document serves as a technical addendum to the "Advanced AI-Assisted Emission Factor Matching: A Context-Driven Approach (Phase 1)" design document. It provides detailed technical specifications for the AI-driven Emission Factor (EF) matching service, focusing on the Phase 1 scope: Material Acquisition (raw and processed materials), Manufacturing Process EFs, and Transportation EFs. End-of-Life (EoL) EF matching is out of scope for this service in Phase 1 and will continue to use its existing separate mechanism.

2. API Contract: EF Matching Service

The AI EF Matching Service will expose an API endpoint with the following specifications:

Endpoint: /ai/ef-match/phase1/recommendations (Suggested endpoint) Method: POST

Request Body (JSON):

{
  "user_query_primary": "string", // Corresponds to Node.name (e.g., "AISI 1020 Steel - Global Steel Corp", "PET", ".25 Fender Washer bundle Transportation Segment 1")
  "user_query_secondary": "string | null", // Optional. Corresponds to Node.component_name (e.g., "PET packaging", "Structural Component") or Node.description
  "lca_lifecycle_stage": "string", // Canonical lifecycle stage value (e.g., "RAW_MATERIAL_ACQUISITION", "MANUFACTURING_PROCESS_APPLICATION", "TRANSPORT_FREIGHT_ROAD", "PACKAGING_MATERIAL_ACQUISITION")
  "iso_code": "string", // Target geography (e.g., "USA", "CN", "RoW")
  "valid_units": ["string"], // List of acceptable units (e.g., ["kg"], ["m2"], ["tkm"])
  "product_category_context": "string | null", // Optional. Broader product category (e.g., "Furniture", "Office Chairs", "Industrial Components")
  "ef_database_source_preference": "string | null" // Optional. Preferred EF DB (e.g., "Ecoinvent 3.11") - for future use in ranking
}

Response Body (JSON):

{
  "matched_activity": { // Primary recommended EF
    "uuid": "string", // EF dataset UUID
    "activity_name": "string",
    "reference_product_name": "string",
    "geography": "string", // Actual geography of the matched EF
    "unit": "string",
    "source_database": "string", // e.g., "Ecoinvent 3.11"
    "isic_classification": "string | null", // e.g., "2410:Manufacture of basic iron and steel", "4923:Freight transport by road"
    // ... other key EF metadata fields deemed useful for display
  } | null, // Null if no confident match found
  "confidence_level": "string | null", // "high", "medium", "low", or null
  "explanation": "string | null", // LLM-generated justification for the primary match
  "alternative_recommendations": [ // List of 0 to N alternative EFs
    {
      "uuid": "string",
      "activity_name": "string",
      "reference_product_name": "string",
      "geography": "string",
      "unit": "string",
      "source_database": "string",
      "isic_classification": "string | null",
      "match_score_or_reasoning": "string | null" // Brief note on why it's an alternative
      // ... other key EF metadata
    }
  ],
  "system_processing_summary": {
    "received_query_primary": "string",
    "received_query_secondary": "string | null",
    "received_lca_lifecycle_stage": "string",
    "predicted_lca_input_category": "string | null", // Output of "Enhanced Input Category Prediction"
    "detected_modifiers": ["string"], // Output of "Modifier Spotting"
    "predicted_isic_classes": ["string"], // List of 4-digit ISIC codes
    "augmented_search_query": "string | null", // Query text used for vector search
    "applied_filters": "object | null" // JSON representation of the ChromaDB where_clause
  },
  "error_message": "string | null" // In case of processing errors
}

3. Detailed Module Logic (Phase 1 Scope)

This section details the internal processing logic for each key AI module as outlined in the main design document's workflow.

Phase 0: Setting the Stage (Handled by Calling Service)

The calling service (e.g., carbonbright-web backend) is responsible for determining the correct canonical lca_lifecycle_stage based on the user's UI context and passing it to this AI service.
Phase 1 Supported lca_lifecycle_stage values (Examples, to be finalized):
RAW_MATERIAL_ACQUISITION
PROCESSED_MATERIAL_ACQUISITION
COMPONENT_ACQUISITION
PACKAGING_MATERIAL_ACQUISITION
MANUFACTURING_PROCESS_APPLICATION
TRANSPORT_FREIGHT_ROAD
TRANSPORT_FREIGHT_SEA
TRANSPORT_FREIGHT_AIR
TRANSPORT_FREIGHT_RAIL
(This list of canonical stage names needs to be finalized and mapped from UI contexts. EoL stages will route to the existing EoL system or return an appropriate message in Phase 1).

Phase 1: Input & Context Processing

Input: user_query_primary, user_query_secondary, lca_lifecycle_stage, product_category_context.

Data Preprocessing: Normalize user_query_primary and user_query_secondary (lowercase, strip excess whitespace).

Step 1A: Enhanced Input Category Prediction (LLM Call)

Input to LLM: user_query_primary, user_query_secondary (if present), lca_lifecycle_stage, product_category_context.
LLM Task: Predict the most specific, LCA-relevant input category.
Phase 1 Canonical Categories (Examples, to be finalized):
Materials: RAW_MATERIAL_CHEMICAL, PROCESSED_MATERIAL_METAL, PACKAGING_MATERIAL_PLASTIC, etc.
Manufacturing: PROCESS_ACTIVITY_METAL_WORKING, PROCESS_ACTIVITY_SURFACE_TREATMENT, etc.
Transport: SERVICE_TRANSPORT_ROAD_FREIGHT, SERVICE_TRANSPORT_SEA_CARGO, ENERGY_FUEL_FOR_TRANSPORT_VEHICLE, etc.
LLM Prompt Sketch: Guides LLM to choose from the defined canonical categories based on inputs.
Output: predicted_lca_input_category (string).

Step 1B: Modifier Spotting (LLM Call)

Input to LLM: user_query_primary, user_query_secondary (if present), predicted_lca_input_category.
LLM Task: Extract key descriptive terms and classify them.
Modifier Categories (Examples):
material_core, material_form_attribute, process_type, origin_attribute, supplier_info, packaging_role.
Transport-specific: transport_mode_explicit (lorry, ship, aircraft, train), vehicle_attribute (>32t, container ship, EURO 6), fuel_type (diesel, HFO, SAF), goods_description.
Output: detected_modifiers (list of strings or key-value pairs).

Step 1C: ISIC Class Prediction/Derivation (LLM Call or Logic)

Input to LLM/Logic: predicted_lca_input_category, detected_modifiers, relevant query parts.
Task: Predict the most relevant 4-digit ISIC Class code(s).
Examples: "2592" for metal coating; "4923" for road freight; "5012" for sea freight (non-coastal).
Output: predicted_isic_classes (list of 4-digit ISIC code strings).

Phase 2: Query & Filter Formulation

Input: Outputs from Phase 1, lca_lifecycle_stage, iso_code, valid_units.

Step 2A: Query Text Augmentation (LLM Call)

Input to LLM: user_query_primary, lca_lifecycle_stage, predicted_lca_input_category, detected_modifiers.
LLM Task: Generate a descriptive query optimized for semantic search.
Example (for Transport query ".25 Fender Washer bundle Transportation Segment 1"): Output might be "road freight transport service for .25 fender washer bundle, first leg, using heavy diesel lorry".
Output: augmented_search_query (string).

Step 2B: Dynamic Filter Construction (Logic)

Input: lca_lifecycle_stage, predicted_lca_input_category, predicted_isic_classes, detected_modifiers, iso_code, valid_units.
Logic: Construct ChromaDB where clause.
activity_type filter: Derived from lca_lifecycle_stage and predicted_lca_input_category.
E.g., if stage is TRANSPORT_FREIGHT_ROAD, activity_type might be {"$in": ["transport activity", "freight transport", "road transport service"]}.
ISIC Classification filter: {"ISIC Classification": {"$regex": "^<CODE>:}} using predicted_isic_classes.
unit filter: {"unit": {"$in": valid_units}}.
Transport-specific metadata filters (if available in ChromaDB and identified by modifiers): e.g., {"transport_mode": "lorry"}, {"fuel_type": "diesel"}.
Output: applied_filters (JSON object).

Phase 3: Vector DB Search (ChromaDB)

Action: Embed augmented_search_query (with relevant instruction for the embedding model, e.g., "Represent this transport service query:") and query emission_activities collection with applied_filters.
Output: List of candidate EF datasets.

Phase 4: LLM Re-ranking & Justification

Input: Original request, Phase 1 outputs, augmented_search_query, candidate EFs with full metadata.
LLM Task: Select best candidate, provide confidence/explanation, considering all context including ISIC alignment and transport-specific attributes if relevant.
Output: selected_activity_uuid, confidence_level, explanation, alternative_recommendations.

Phase 5: Geography Matching & Final Record Retrieval

Action: As defined previously (query efs_with_geographies, prioritize).
Output: Fully detailed EF record(s).

Phase 6: Final Output to User (API Response Construction)

Action: Assemble API response as defined in Section 2 (API Contract).

Phase 7: Learning & Improvement (Evaluation & Iterative Refinement Cycle)

Specification: As detailed in the main design document (Section 4.2, Phase 7). Focus on golden dataset creation, regular evaluation, root cause analysis, and iterative manual tuning (prompt engineering, logic adjustments).

4. Data Preprocessing & Handling (User Query)

Input strings (user_query_primary, user_query_secondary) consistently preprocessed: lowercase, strip whitespace, normalize multiple spaces.

5. Error Handling & Fallbacks

Robust error handling for LLM calls and database interactions.
Graceful degradation or broader searches with lower confidence if specific predictions/filters fail or yield no results.


Implementation Approach for AI-Assisted EF Matching Service (Phase 1)
This section outlines a recommended approach for implementing Phase 1 of the "Advanced AI-Assisted Emission Factor Matching System," focusing on Material Acquisition, Manufacturing Process EFs, and Transportation EFs. It leverages existing codebase components where possible and introduces new logic as per the revised design. The primary point of intervention will be the evolution of the existing /activities/recommendations endpoint and its underlying logic in the emissions_factor_matching module.

1. Prerequisites & Setup:

Define Canonical Lists: Finalize the canonical lists for:
lca_lifecycle_stage values supported in Phase 1 (e.g., RAW_MATERIAL_ACQUISITION, PROCESSED_MATERIAL_ACQUISITION, MANUFACTURING_PROCESS_APPLICATION, TRANSPORT_FREIGHT_ROAD, etc.).
predicted_lca_input_category values the new LLM will output (e.g., RAW_MATERIAL_CHEMICAL, PROCESSED_MATERIAL_METAL, PROCESS_ACTIVITY_SURFACE_TREATMENT, SERVICE_TRANSPORT_ROAD_FREIGHT).
modifier_categories for the "Modifier Spotting" LLM (e.g., material_core, process_type, transport_mode_explicit, vehicle_attribute).
API Contract Finalization: Confirm the request/response schema for the new EF matching service endpoint (as outlined in TDD Section 2).
Environment Configuration: Ensure API keys and model deployment endpoints (Azure OpenAI) are accessible via config.py.

2. Modifying the Main EF Matching Endpoint & Workflow:

The existing endpoint emissions_factor_matching.api.get_recommended_activities (serving /activities/recommendations) will be refactored.

Step 0: Adapt Request Handling:
Update ActivityRecommendationsRequest Pydantic model to include lca_lifecycle_stage (mandatory) and user_query_secondary (optional).
Endpoint logic checks if lca_lifecycle_stage is in Phase 1 scope (Materials, Mfg, Transport). If 'EndOfLifeTreatment', route to existing EoL system or return appropriate message for Phase 1.
Step 1: Implement New "Input & Context Processing" Phase:
Replace existing initial classification/enrichment steps with the new multi-LLM sequence:
1A. Enhanced Input Category Prediction: New function in emissions_factor_matching/predictions.py using LLM (via clients.py, completions.py) and new prompt.
1B. Modifier Spotting: New function, new prompt.
1C. ISIC Class Prediction/Derivation: New function, new prompt/logic, leveraging ISIC Classification metadata knowledge.
Step 2: Implement New "Query & Filter Formulation" Phase:
2A. Query Text Augmentation: New function in emissions_factor_matching/predictions.py, new prompt.
2B. Dynamic Filter Construction: New Python logic replacing current filter part in get_recommended_activities. Uses all contextual inputs (stage, category, ISIC, modifiers, units, geo) to build where clause.
Step 3: Adapt "Vector DB Search" Phase:
Reuse collection.query(...).
Pass new augmented_search_query and new dynamic where clause.
Review/update INSTRUCTION_PROMPT in emissions_factor_matching/model.py for embedding.
Step 4: Adapt "LLM Re-ranking" Phase:
Significantly update prompt for get_closest_match in emissions_factor_matching/predictions.py to use all new rich context (stage, category, modifiers, ISIC, augmented query) and full candidate metadata.
Step 5: "Geography Matching" Phase:
Reuse get_geography_activity_match from emissions_factor_matching/geography.py.
Step 6: "Response Assembly" Phase:
Adapt to use new workflow outputs and populate the revised/new ActivityRecommendationsResponse model, including system_processing_summary.
Step 7: "Learning & Improvement":
Data Logging: Crucial initial step. Log API inputs, all intermediate AI outputs, ChromaDB candidates, final selection comprehensively.
Manual tuning (prompt engineering, logic adjustments) based on golden dataset evaluation.

3. Development & Testing Strategy:

Modular development and unit testing of new/refactored prediction, augmentation, and filtering functions.
Integration testing of the end-to-end /activities/recommendations flow with diverse Phase 1 test cases (various materials, mfg processes, transport scenarios).
Iterative prompt engineering.
Evaluation against a "golden dataset" (from user overrides, "Ideal EF matches" spreadsheet).
Maximize reuse of existing utilities.

4. Key Files for Modification/Reference (in ml-models repo):

Primary modification: emissions_factor_matching/api.py (get_recommended_activities, Pydantic models).
New/Heavily modified functions in: emissions_factor_matching/predictions.py.
Potential updates to: emissions_factor_matching/model.py.
Leverage existing: dataset.py, geography.py, clients.py, completions.py, config.py, utils/.

5. Out of Scope for Phase 1 of this New AI System (Reminder):

Changes to /eol/activity or collection_eol handling (existing separate EoL system remains).
Full LLM fine-tuning (focus on prompt engineering and logic tuning).

This approach refactors the core EF matching pipeline to incorporate deep contextual understanding for Phase 1 deliverables (Materials, Manufacturing, Transport), setting up a robust framework for ongoing improvement.




