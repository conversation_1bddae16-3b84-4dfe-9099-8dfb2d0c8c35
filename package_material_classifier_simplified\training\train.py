import time
import torch
from training.data_loaders import train_loader
from training.model import (
  resnet50,
  loss_function,
  optimizer,
  device,
)

def train(num_epochs: int):
    for epoch in range(num_epochs):

        running_loss = 0.0
        for inputs, labels in train_loader:
            inputs = inputs.to(device)
            labels = labels.to(device)

            optimizer.zero_grad()

            outputs = resnet50(inputs)
            loss = loss_function(outputs, labels)

            loss.backward()
            optimizer.step()

            running_loss += loss.item() * inputs.size(0)

        epoch_loss = running_loss / len(train_loader.dataset)

        print(f"Epoch: {epoch+1}, Loss: {epoch_loss:.4f}")

    timestamp = time.time()
    torch.save(resnet50.state_dict(), f"weights/packaging_classifier_simplified_{timestamp}.pth")