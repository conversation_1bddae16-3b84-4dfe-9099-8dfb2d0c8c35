# Emission Factor Matching Flow: Tracing Outputs at Each Stage

This document traces the complete flow of the emission factor matching process, showing the outputs at each stage from initial user request to final recommendation.

## 1. User Request Flow

### Stage 1: Frontend UI Request
- **Input**: User submits a request with parameters like `chemical_name`, `geography`, etc.
- **Output**: GraphQL query `predictEmissionsFactors` is sent to backend

### Stage 2: Backend API Endpoint Handler
- **Input**: GraphQL query parameters
- **Output**: A call to `PredictionOverrideService.get_emissions_factor_match_override(query=chemical_name)` is made

### Stage 3: Prediction Override Check
- **Input**: User's `chemical_name` and tenant ID
- **Output**: 
  - If override exists: `TenantEmissionsFactor` object is returned
  - If no override: `None` is returned
- **Data Structure**: Override JSON containing `activity_name`, `geography`, `reference_product_name`, `source`

## 2. Emission Factor Service Flow

### Stage 4: Main Service Entry Point
- **Input**: User parameters and possibly an override from Stage 3
- **Output**: Final `Activity` object with `match_type` set to one of: "OVERRIDE", "CURATED", or "RECOMMENDATION"

### Stage 5: Curated Match Check (if no override)
- **Input**: `ingredient_name` from user request
- **Output**: 
  - If match found: `Activity` object from database with `match_type = "CURATED"`
  - If no match: Proceeds to ML service
- **Data Structure**: SQL query result from `emissions_factors` table

## 3. ML Service Flow (if no override or curated match)

### Stage 6: Input Classification 
- **Input**: `chemical_name` from user request
- **Output**: Binary classification - "CHEMICAL" or "PRODUCT"
- **Data Structure**: Plain text from LLM call
- **Example Output**: 
  ```
  PRODUCT
  ```

### Stage 7: Input Enrichment
- **Input**: `chemical_name` and classification from Stage 6
- **Output**: Enriched query text with additional context
- **Data Structure**: String
- **Example for Products**:
  ```
  "Coffee for beverages - A dark, brewed beverage made from roasted coffee beans, typically containing caffeine"
  ```
- **Example for Chemicals**:
  ```
  "Sodium Chloride (salt, sodium chloride, halite, rock salt): A crystalline compound used as a seasoning and preservative, composed of sodium and chloride ions."
  ```

### Stage 8: ChromaDB Vector Search
- **Input**: Enriched query from Stage 7 with filters (activity type, geography, etc.)
- **Output**: Top matching candidates (typically 25)
- **Data Structure**: ChromaDB response with `ids`, `documents`, `metadatas`, and distances
- **Example Output**:
  ```json
  {
    "ids": [["ef-12345", "ef-23456", "ef-34567", ...]],
    "documents": [["coffee production", "coffee roasting", "beverage production", ...]],
    "metadatas": [[
      {"uuid": "ef-12345", "reference_product_name": "coffee", "source": "Ecoinvent", ...},
      {"uuid": "ef-23456", "reference_product_name": "roasted coffee", "source": "Ecoinvent", ...},
      ...
    ]],
    "distances": [[0.123, 0.234, 0.345, ...]]
  }
  ```

### Stage 9: LLM-Based Re-Ranking
- **Input**: Original query and candidates from Stage 8
- **Output**: LLM selected best match with explanation
- **Data Structure**: JSON object
- **Example Output**:
  ```json
  {
    "activity_uuid": "ef-12345",
    "confidence": "high",
    "match_explanation": "This activity directly represents coffee production, including the harvesting and processing of coffee beans into a consumable product. It's the most specific match for 'coffee' as a product."
  }
  ```

### Stage 10: Geography-Specific Matching
- **Input**: Selected activity name and user's geography (`iso_code`)
- **Output**: Geography-specific version of the activity
- **Data Structure**: Dictionary from dataframe lookup
- **Example Output**:
  ```json
  {
    "Activity Name": "coffee production",
    "Activity UUID": "ef-12345-US",
    "Reference Product Name": "coffee",
    "Product Information": "...",
    "Source": "Ecoinvent",
    "Geography": "US"
  }
  ```

### Stage 11: Response Assembly
- **Input**: Data from Stages 9 and 10, plus other candidates
- **Output**: Complete response with main match and alternatives
- **Data Structure**: `ActivityRecommendationsResponse`
- **Example Output**:
  ```json
  {
    "matched_activity": {
      "activity_name": "coffee production",
      "activity_uuid": "ef-12345-US",
      "reference_product_name": "coffee",
      "product_information": "...",
      "source": "Ecoinvent",
      "geography": "US"
    },
    "confidence": "high",
    "explanation": "This activity directly represents coffee production...",
    "recommendations": [
      {
        "activity_name": "coffee roasting",
        "activity_uuid": "ef-23456-US",
        "reference_product_name": "roasted coffee",
        "product_information": "...",
        "source": "Ecoinvent",
        "geography": "US",
        "similarity": 0.234
      },
      ...
    ]
  }
  ```

## 4. Optional: Technical Quality Assessment

### Stage 12: Technological Representation Analysis
- **Input**: User's process description and matched activity name
- **Output**: Technical quality assessment
- **Data Structure**: Four comparison results evaluating similarity across:
   1. Production Process Design
   2. Production Operating Conditions
   3. Material Quality/Type
   4. Production Process Scale
- **Example Output**:
  ```json
  {
    "process_design_comparison": {
      "reason": "Both involve roasting of coffee beans at high temperatures...",
      "similar": true
    },
    "operating_condition_comparison": {
      "reason": "The temperature and timing requirements match closely...",
      "similar": true
    },
    "material_quality_comparison": {
      "reason": "Both specify high-quality arabica beans...",
      "similar": true
    },
    "process_scale_comparison": {
      "reason": "The commercial scale of operation differs significantly...",
      "similar": false
    },
    "technical_match_score": 0.75
  }
  ```

## 5. Implementation Details

### Key Components and Files

#### Core Emission Factor Matching Files
- `emissions_factor_matching/api.py` - FastAPI endpoints for the emission factor matching service
- `emissions_factor_matching/dataset.py` - Vector database configuration and data loading
- `emissions_factor_matching/model.py` - Embedding model and instruction prompts
- `emissions_factor_matching/predictions.py` - Core ML/LLM prediction logic
- `emissions_factor_matching/geography.py` - Geography hierarchy and matching logic
- `emissions_factor_matching/prompt.py` - Prompts for LLM interactions

#### Key ML Service Features

1. **Vector Database and Embedding Framework**
   - **ChromaDB** as the vector database
   - **Collections**: `emission_activities` (standard EFs) and `collection_eol` (end-of-life EFs)
   - **Embedding Model**: "hkunlp/instructor-large" with task-specific instruction prompts
   ```python
   INSTRUCTION_PROMPT = "Retrieve the closest emissions activity name provided a product description based on product inputs and manufacturing methods, consider suitable proxies:"
   ```

2. **Multiple LLM Calls**
   The system makes several distinct LLM calls during processing:
   - Input categorization (chemical vs. product)
   - Retrieving common chemical names (for chemicals)
   - Generating product descriptions
   - ISIC section prediction
   - Re-ranking candidates
   - Technical quality assessment (4 separate comparisons)

3. **Hybrid Retrieval-Ranking Architecture**
   - Vector similarity search for initial retrieval
   - LLM-based re-ranking for final selection
   - Structured constraints (filters) applied to the vector search

4. **Geography Hierarchy**
   A complex geography mapping system handles finding the most appropriate regional variant of a matched emission factor.

5. **Performance Optimization**
   The system implements caching for resource-intensive operations to improve response times.

## 6. Limitations of Current Classification

The current binary classification system (product vs. chemical) has several limitations:

1. **Energy Inputs and Services**: Inputs like electricity, heat, natural gas, transportation services, or waste management services don't fit neatly into either chemical or product categories.

2. **Natural Resources and Raw Materials**: Items like mineral ores, agricultural products, or water sources may not be accurately classified.

3. **Processes and Manufacturing Activities**: The system doesn't have a direct category for inputs that represent processes rather than materials (e.g., "Steel Welding" or "Heat Treatment").

4. **Mixed or Composite Materials**: While the system attempts to handle composite products by predicting constituents, it lacks a dedicated classification for standardized mixed materials.

5. **Infrastructure and Capital Goods**: Capital goods and infrastructure elements have significant lifecycle impacts but don't fit well in the existing binary categories.

## 7. Testing and Validation

The system includes a comprehensive test suite (`emissions_factor_matching/tests/`) that validates its performance against known real-world examples. For example, the `test_activity_prediction.py` file contains a list of chemical inputs and their expected activity matches:

```python
real_world_validations = [
    ("non-ionic polyol", "non-ionic surfactant production, ethylene oxide derivate"),
    ("Lauramine Oxide", "non-ionic surfactant production, fatty acid derivate"),
    ("Amylase", "enzymes production"),
    # ... and many more
]
```

These tests ensure the system's accuracy across a wide range of different input types and help identify areas for improvement.
