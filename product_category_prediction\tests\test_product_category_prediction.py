from unittest import TestCase
from utils import logger
from product_category_prediction.predictions import predict_product_category

validation_categories = [
    (
        "Crest Cavity Protection Toothpaste Gel, Cool Mint",
        [
            "Toothpaste"
        ],
    ),
    (
        "Harry's Shave Gel - Shaving Gel with an Aloe Enriched Formula - 3 pack (6.7oz)",
        [
            "Shaving Cream"
        ],
    ),
    (
        "Amazon Basics - 2 Piece Quick-Dry Oversize Bath Towel",
        [
            "Bath Towels & Washcloths"
        ],
    ),
    (
        "Nespresso Capsules Vertuo, Melozio, Medium Roast Coffee, 40-Count Coffee Pods",
        [
            "Coffee"
        ],
    ),
    (
        "Persil Non Bio Liquid Laundry Washing Detergent 95 Washes",
        [
            "Laundry Detergent"
        ]
    ),
    (
        "TRESemme Lamellar Shine Shampoo",
        [
            "Shampoo"
        ]
    ),
    (
        "MONDAY Smooth Conditioner",
        [
            "Conditioners"
        ]
    ),
    (
        "Whole Foods Market, Slim Thermal Shopping Bag, Small, 4 Gallon",
        [
            "Shopping Totes"
        ]
    ),
    (
        "Delphis Eco Multi Purpose Spray",
        [
            "All-Purpose Cleaners"
        ]
    ),
    (
        "Miniml Multi-Surface Cleaner Blood Orange",
        [
            "All-Purpose Cleaners",
            "Glass & Surface Cleaners"
        ]
    ),
    (
        "Sunnyglade 9' Patio Umbrella Outdoor Table Umbrella with 8 Sturdy Ribs (Red)",
        [
            "Parasols & Rain Umbrellas"
        ]
    ),
    (
        "Amazon Basics Stainless Steel Insulated Water Bottle With Spout Lid, 30 ounce, Large Size, Gray",
        [
            "Water Bottles",
            "Thermoses",
            "Hydration Systems"
        ]
    )
]


class TestProductCategoryPrediction(TestCase):
    def test_product_category_predictions(self):
        for product_name, validation_category in validation_categories:
            category = predict_product_category(product_name)
            self.assertTrue(category[-1] in validation_category)
