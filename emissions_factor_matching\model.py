from chromadb.utils import embedding_functions
from pydantic import BaseModel
from typing import Dict, Any, Optional

INSTRUCTION_PROMPT = "Retrieve the closest emissions activity name provided a product description based on product inputs and manufacturing methods, consider suitable proxies:"

embedding_function = embedding_functions.InstructorEmbeddingFunction(
    model_name="hkunlp/instructor-large",
    instruction=INSTRUCTION_PROMPT,
)

EOL_INSTRUCTION_PROMPT = """Retrieve the waste disposal activity which most closely matches a provided material given the dispoal method:"""

eol_embedding_function = embedding_functions.InstructorEmbeddingFunction(
    model_name="hkunlp/instructor-large",
    instruction=EOL_INSTRUCTION_PROMPT,
)


class Candidate(BaseModel):
    """
    Data model for Phase 1.6: ChromaDB Vector Search candidates

    Represents a candidate emission factor activity returned from ChromaDB search
    with all necessary metadata for downstream processing.
    """
    # Core identifiers
    activity_uuid: str
    chroma_id: str

    # Activity information
    activity_name: str
    reference_product_name: str
    product_information: Optional[str] = None

    # Source and classification
    source: str
    isic_4_code: Optional[str] = None
    isic_section: Optional[str] = None
    activity_type: str

    # Geography and units
    geography: Optional[str] = None
    unit: Optional[str] = None

    # Search relevance
    distance: float  # ChromaDB distance score (lower = more similar)
    similarity_score: Optional[float] = None  # Computed as 1 - distance

    # Additional metadata
    metadata: Dict[str, Any] = {}

    def __post_init__(self):
        """Compute similarity score from distance"""
        if self.similarity_score is None and self.distance is not None:
            self.similarity_score = max(0.0, 1.0 - self.distance)


class MatchedEF(BaseModel):
    """
    Data model for final matched emission factor after all phases

    Represents the final selected emission factor with confidence and explanation
    from the AI-assisted matching pipeline.
    """
    # Core activity information (inherited from Candidate)
    activity_uuid: str
    activity_name: str
    reference_product_name: str
    product_information: Optional[str] = None
    source: str
    geography: Optional[str] = None
    unit: Optional[str] = None

    # AI-assisted matching results
    confidence: str  # "HIGH", "MEDIUM", "LOW"
    confidence_score: Optional[float] = None  # 0.0 to 1.0
    explanation: str  # LLM explanation for the match

    # Search and ranking information
    original_distance: float  # Original ChromaDB distance
    final_rank: int  # Final rank after LLM re-ranking

    # Processing metadata
    processing_metadata: Dict[str, Any] = {}
