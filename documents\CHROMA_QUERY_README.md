# ChromaDB Query Tools

This directory contains tools for querying the ChromaDB collections used in the emissions factor matching functionality. These tools run directly in your existing `ml-models-app` container and use the already-loaded embedding functions for fast queries.

## Prerequisites

- Docker installed
- The `ml-models-app` container must be running
- The query script must be copied to your project directory

## Initial Setup

Before running any queries, you must first copy the query script to the container:

```powershell
# Copy the query script to the container
.\copy-to-container.ps1
```

This step is required only once, or if you make changes to the query script.

## Available Scripts

### PowerShell Script (Recommended for Windows)

For PowerShell users, use this script:

```powershell
# Run with default parameters
.\Run-Query.ps1

# Run with custom parameters
.\Run-Query.ps1 --query_text "your query text" --n_results 10 --display_count 5

# Run the steel query example
.\Run-Query.ps1 --query_text "The primary material is steel. steel" --n_results 125 --display_count 10
```

### Batch File (Alternative for Windows)

For Command Prompt users, use this batch file:

```cmd
# Run with default parameters
.\run_query.bat

# Run with custom parameters
.\run_query.bat --query_text "your query text" --n_results 10 --display_count 5

# Run the steel query example
.\run_query.bat --query_text "The primary material is steel. steel" --n_results 10 --display_count 10
```

### Direct Docker Commands (Linux/Mac)

For Linux/Mac users:

```bash
# Run with default parameters
docker exec -it ml-models-app python /home/<USER>/app/query_chroma_existing_collection.py

# Run with custom parameters
docker exec -it ml-models-app python /home/<USER>/app/query_chroma_existing_collection.py --query_text "your query text" --n_results 10 --display_count 5

# Run the steel query example
docker exec -it ml-models-app python /home/<USER>/app/query_chroma_existing_collection.py --query_text "The primary material is steel. steel" --n_results 10 --display_count 10
```

## Available Parameters

The query script supports the following parameters:

- `--collection_name`: Name of the ChromaDB collection to query (default: "emission_activities", can also be "eol_activities")
- `--query_text`: Query text to search for
- `--where_clause`: JSON string of the where clause for filtering
- `--n_results`: Number of results to return (default: 10)
- `--save_results`: Save full results to a JSON file
- `--output_file`: File to save results to if save_results is True (default: "chroma_query_results.json")
- `--display_count`: Number of top results to display (default: 5)

## Example Queries

### Default Query (Hexafluoroethane-based polymer)

```powershell
.\Run-Query.ps1
```

### Steel Query

```powershell
.\Run-Query.ps1 --query_text "The primary material is steel. steel" --n_results 10 --display_count 10
```

### Complex Polymer Query with Activity Type Filter

```powershell
.\Run-Query.ps1 -query_text "1,4-Butanediol, polymer with ?-hydro-?-hydroxypoly( oxy-1,4-butanediyl) and 1,1'-methylenebis[4-isocyanatobenzene] disposal (incineration)" -where_clause '{"activity_type": {"$eq": "ordinary transforming activity"}}' -n_results 5
```

### Custom Where Clause

```powershell
.\Run-Query.ps1 --query_text "aluminum" --where_clause "{\"activity_type\": {\"$eq\": \"ordinary transforming activity\"}}" --n_results 20
```

### Save Results to File

```powershell
.\Run-Query.ps1 --query_text "plastic" --save_results --output_file "plastic_results.json"
```

### Query End-of-Life Activities

```powershell
.\Run-Query.ps1 --collection_name "eol_activities" --query_text "plastic recycling" --n_results 20
```

## Troubleshooting

If you encounter issues:

1. Make sure the `ml-models-app` container is running
2. Verify that the query script is in your project directory
3. **Important**: If you get a "No such file or directory" error, make sure you've run the `copy-to-container.ps1` script first to copy the query script into the container
4. Check that the collection name matches what's in your ChromaDB database
5. Ensure the where clause is properly formatted as a JSON string
6. In PowerShell, if you get an execution policy error, run:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
   ```
   Then run the script again
