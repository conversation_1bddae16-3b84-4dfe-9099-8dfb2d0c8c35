from unittest import TestCase
from emissions_factor_matching.geography import root

class TestGeography(TestCase):
    def test_priority_geography_list(self):
        iso_code = "GB"
        geography = root.find_geography(iso_code)

        self.assertEqual(
            geography.get_priority_geographies(),
            [
                "GB",
                "RER",
                "GLO",
                "RoW",
            ]
        )
