# ML Models Architecture

This document provides an overview of the ML Models architecture, including what happens during startup, the components involved, and how they interact.

## System Overview

The ML Models service is a collection of machine learning APIs that provide various capabilities for sustainability and life cycle assessment (LCA) tasks. The service is containerized using Docker and consists of two main containers:

1. **ML Models App** - The main application container that hosts the ML models and APIs
2. **Redis** - A caching layer to improve performance by storing API responses

## Container Architecture

```
┌─────────────────────────────────────────┐
│                Docker                    │
│                                         │
│  ┌─────────────────┐  ┌──────────────┐  │
│  │  ML Models App  │  │    Redis     │  │
│  │                 │◄─┤              │  │
│  │  (FastAPI)      │  │  (Cache)     │  │
│  └────────┬────────┘  └──────────────┘  │
│           │                             │
└───────────┼─────────────────────────────┘
            │
            ▼
┌─────────────────────────────────────────┐
│        External Services                 │
│                                         │
│  ┌─────────────┐  ┌───────────────────┐ │
│  │ Azure OpenAI│  │  Hugging Face Hub │ │
│  └─────────────┘  └───────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

## Startup Process

When you start the ML services using Docker Compose, the following sequence of events occurs:

### 1. Container Setup and Initialization

#### Redis Container

- A Redis container is started using the Redis 6 image
- It's configured using a custom configuration file (`./scripts/config/redis.conf`)
- It's exposed on port 6379
- Redis serves as a caching layer for the ML services to improve performance

#### ML Models App Container

- The main application container is built using the Dockerfile in the project root
- It uses Python 3.10 as the base image
- System dependencies are installed (poppler-utils, tesseract-ocr, libgl1-mesa-glx, pandoc)
- Python dependencies are installed from the requirements.txt file
- The container is exposed on port 5001
- In development mode, the local codebase is mounted to the container, enabling hot reloading

### 2. Environment Configuration

- The application loads environment variables from the `.env` file
- Critical variables include:
  - Azure OpenAI API keys and endpoints
  - Hugging Face token
  - Azure Phi-3 endpoint and key
  - Mapbox access token
  - Redis URL configuration
  - Various deployment model names (GPT-3.5, GPT-4, GPT-4o)

### 3. Model and Data Downloads

When the application starts, it downloads several models and datasets:

#### Hugging Face Authentication

- The application logs in to Hugging Face using your token: `login(token=config.hf_token)`

#### NLTK Downloads

- Downloads natural language processing resources:
  ```python
  nltk.download('punkt')
  nltk.download('punkt_tab')
  nltk.download('averaged_perceptron_tagger')
  nltk.download('averaged_perceptron_tagger_eng')
  ```

#### Package Material Classifier Model

- Downloads a ResNet50 model from Hugging Face:
  - Repository: "CarbonBright/packaging-base-material-classification"
  - File: "packaging_classifier_64_epochs_weights.pth"
- Initializes the model for image classification of packaging materials

#### Emissions Factor Matching Data

- Downloads ChromaDB vector databases:
  - "chroma_with_instructor_embeddings_v2.zip"
  - "chroma_with_instructor_embeddings_eol.zip"
- Downloads emissions factors dataset:
  - "emissions_factors_with_geographies_v2.pkl"
- Initializes embedding models:
  - "hkunlp/instructor-large" for emissions activity matching
  - "hkunlp/instructor-large" for end-of-life activity matching

#### Product Category Data

- Downloads product categories data:
  - Repository: "CarbonBright/exploration"
  - File: "product_categories.json"

#### Manufacturing Processes Data

- Downloads manufacturing processes data:
  - Repository: "CarbonBright/database-seed-data"
  - File: "manufacturing_processes.csv"

### 4. API Initialization

The application initializes a FastAPI server with several mounted sub-applications:

1. **Package Material Classifier API** (`/api/package-material-classifier-simplified`)

   - Classifies packaging materials from images using a ResNet50 model

2. **Chemical Prediction API** (`/api/chemical-prediction`)

   - Predicts HS codes for chemicals
   - Predicts CAS numbers
   - Classifies materials

3. **Emissions Factor Matching API** (`/api/emissions-factor-matching`)

   - Matches emissions factors for products and activities
   - Provides geography-specific emissions data
   - Handles end-of-life activity recommendations

4. **Product Category Prediction API** (`/api/product-category-prediction`)

   - Predicts product categories based on product names
   - Uses Azure OpenAI models (GPT-4o)

5. **Product Manufacturing API** (`/api/product-manufacturing`)

   - Predicts manufacturing processes for products
   - Uses Azure OpenAI models

6. **File Extraction API** (`/api/file-extraction`)
   - Extracts structured data from files (PDFs, etc.)
   - Extracts bill of materials (BOM) information
   - Uses Unstructured library for document parsing

#### File Extraction API Details

The File Extraction API is a sophisticated component that extracts structured data from uploaded files, particularly focusing on PDF documents and CSV files. It's designed to extract bill of materials (BOM) information, product details, and component relationships.

**Endpoint Structure:**

1. **Main Endpoint (`POST /`)**:

   - Extracts product information from uploaded files
   - Supports different file types, primarily PDFs and CSVs
   - Can operate in two modes based on the `x_parts_only` header flag

2. **Components Endpoint (`POST /components`)**:
   - Specifically extracts component information from CSV or Excel files
   - Returns a structured list of components with their relationships

**Extraction Process:**

1. **File Processing**:

   - The uploaded file is saved to a temporary location
   - The file's content type is determined (PDF, CSV, etc.)

2. **Extraction Mode Selection**:

   - If the `x_parts_only` header is set to `true`, it calls `get_parts_from_file()` which extracts just the parts list
   - Otherwise, it calls `get_product_output_from_file()` which performs a comprehensive extraction

3. **Document Parsing**:

   - For comprehensive extraction, the file is partitioned using the `unstructured` library
   - The document is broken down into elements (text blocks, tables, etc.)

4. **Content Analysis**:

   - The system analyzes whether the file contains structured tables
   - For CSV files with structured tables, it uses a specialized extraction method
   - For other files, it filters relevant sections and extracts information using LLMs

5. **LLM-Based Extraction**:

   - Uses Azure OpenAI's GPT-4o model to extract structured data
   - Identifies product information, raw materials, packaging components, etc.
   - Constructs a graph of nodes (components, materials) and edges (relationships)

6. **Response Generation**:
   - Returns a structured JSON response with product details, nodes, and edges
   - Includes information like product name, ID, factory location, etc.

**Key Dependencies:**

- **Azure OpenAI Integration**: Uses GPT-4o model for text analysis and extraction
- **Mapbox Integration**: Used for location data enrichment
- **Unstructured Library**: Used for document parsing and initial content extraction

**Data Models:**

- **Component Model**: Represents a component with its ID, name, nodes, and edges
- **Node Model**: Represents entities like materials, production processes, etc.
- **Edge Model**: Represents relationships between nodes
- **BOMItems Model**: Contains lists of raw materials and packaging components
- **ProductInfo Model**: Contains product details like name, ID, factory location, etc.

**Implementation Details:**

- The main LLM calls are made in `file_extraction/predictions.py`
- The actual API calls to Azure OpenAI are handled by `completions.py`
- Configuration for the Azure OpenAI API is defined in `config.py`
- The model used is specified by `config.azure_openai_deployment`, which defaults to "gpt-4o"

### 5. Middleware and Configuration

- Sets up CORS middleware to allow cross-origin requests
- Configures logging middleware to track API requests and responses
- Sets up error handling for both HTTP and general exceptions
- Initializes Sentry for error tracking (if configured)

### 6. Network Configuration

- Both containers are connected to a Docker network called `ml-models-network`
- This allows the ML models container to communicate with Redis
- There's also a script (`connect_docker_networks.py`) that can connect the ML models container to the LCA network if needed

### 7. Debugging Configuration (Optional)

- If `DEBUG=true` is set, remote debugging is enabled on port 5678
- The application will wait for a debugger to attach before proceeding

### 8. Server Startup

- The application starts a Uvicorn server on port 5001 (or as configured)
- The number of workers is determined by the `WORKERS` environment variable (default: 2)

## API Architecture

Each API endpoint is implemented as a separate FastAPI application that is mounted to the main application. This modular approach allows for better organization and separation of concerns.

### Common Patterns

1. **Caching**

   - Most API endpoints use Redis for caching responses
   - The `@cached` decorator is used to cache API responses
   - Cache keys are prefixed with the API name (e.g., `chem_`, `pc_`, `pm_`)

2. **Model Loading**

   - Models are loaded at import time
   - This ensures they are ready when the API starts
   - Downside: increases startup time

3. **Error Handling**
   - Global exception handlers catch and log errors
   - Structured error responses are returned to clients

## Data Flow

1. **Client Request** → The client sends a request to one of the API endpoints
2. **Cache Check** → The system checks if the response is cached in Redis
3. **Model Inference** → If not cached, the appropriate model is used to generate a response
4. **Cache Storage** → The response is stored in Redis for future requests
5. **Client Response** → The response is returned to the client

## Key Components

### 1. FastAPI Framework

The application uses FastAPI, a modern, high-performance web framework for building APIs with Python. FastAPI provides:

- Automatic API documentation
- Request validation
- Dependency injection
- High performance with async support

### 2. Redis Cache

Redis is used as a caching layer to improve performance by storing API responses. This reduces the need to run expensive model inference for repeated requests.

### 3. ML Models

The application uses a variety of ML models:

- **Custom trained models** (package material classifier)
- **Vector databases with embeddings** (emissions factor matching)
- **Azure OpenAI models** (GPT-3.5, GPT-4, GPT-4o)
- **Azure Phi-3 model**

### 4. Hugging Face Integration

The application uses Hugging Face Hub to download and manage models and datasets.

## Development vs. Production

### Development Mode

In development mode (using docker-compose.dev.yml):

- Local codebase is mounted to the container
- Code changes are immediately reflected without rebuilding
- Debugging is enabled

### Production Mode

In production mode (using docker-compose.yml):

- Code is copied into the container during build
- Changes require rebuilding the container
- Optimized for stability and security
