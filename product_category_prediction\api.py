from fastapi import FastAPI
from product_category_prediction.dataset import product_categories
from product_category_prediction.predictions import predict_product_category
from utils.cache import create_cache, cached

model_api = FastAPI()
cache = create_cache(prefix='pc_')

@model_api.get("/product-name/{product_name:path}")
@cached(cache)
def predict_category(product_name: str):
    product_category = predict_product_category(product_name)
    return {"category": product_category}
