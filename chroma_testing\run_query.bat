@echo off
echo Running ChromaDB Query in existing ml-models-app container...

REM Check if arguments were provided
if "%~1"=="" (
    echo No custom query provided. Running with default parameters...
    docker exec -it ml-models-app python /home/<USER>/app/query_chroma_existing_collection.py
) else (
    echo Running with custom query: %*
    docker exec -it ml-models-app python /home/<USER>/app/query_chroma_existing_collection.py %*
)

echo Done!
pause
