# Setting Up ML Models Repository Locally

This document outlines the steps to set up the ML Models repository locally using Docker.

## Prerequisites

- Docker Desktop installed and running
- Git (to clone the repository)
- .env file with required credentials

## Environment Variables

The following environment variables are required in your .env file:

```
AZURE_OPENAI_API_KEY="your-key"
AZURE_OPENAI_API_KEY_US_EAST="your-key"
AZURE_OPENAI_ENDPOINT="your-endpoint"
AZURE_OPENAI_ENDPOINT_US_EAST="your-endpoint"
HF_TOKEN="your-huggingface-token"
AZURE_OPENAI_DEPLOYMENT="3_5_turbo"
AZURE_OPENAI_DEPLOYMENT_4="GPT-4-Turbo"
AZURE_PHI_3_ENDPOINT="your-endpoint"
AZURE_PHI_3_KEY="your-key"
MAPBOX_ACCESS_TOKEN="your-token"
PORT=5001
REDIS_URL="redis://ml-model-redis:6379"
```

## Setup Steps

### 1. <PERSON>lone the Repository

```bash
<NAME_EMAIL>:CarbonBright/ml_models.git
cd ml_models
```

### 2. Create .env File

Create a `.env` file in the root directory with the required environment variables (see above).

### 3. Docker Compose Setup

We'll use Docker Compose to set up both the Redis container and the ML models application in the same network, ensuring they can communicate with each other.

### 4. Docker Compose Configuration

The repository includes two Docker Compose configurations:

1. **docker-compose.yml** - For production-like environments
2. **docker-compose.dev.yml** - For development with volume mounting

For development, we recommend using `docker-compose.dev.yml` which includes volume mounting to allow code changes without rebuilding the container:

```yaml
version: "3"

services:
  redis:
    image: redis:6
    container_name: ml-model-redis
    ports:
      - "6379:6379"
    volumes:
      - ./scripts/config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ml-models-network

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ml-models-app
    ports:
      - "5001:5001"
    env_file:
      - .env
    environment:
      - REDIS_URL=redis://ml-model-redis:6379
      # Disable debugging to prevent waiting for debugger
      - DEBUG=false
    volumes:
      # Mount the local codebase to the container's working directory
      - ./:/home/<USER>/app
      # Exclude the virtual environment directory if you have one locally
      - /home/<USER>/app/venv
      # Exclude any other directories that shouldn't be mounted
      - /home/<USER>/app/__pycache__
    depends_on:
      - redis
    dns:
      - *******
      - *******
    networks:
      - ml-models-network

networks:
  ml-models-network:
    name: ml-models-network # This forces a specific name without the project prefix
    driver: bridge
```

### 5. Build and Start the Application

Build and start the application container using the development configuration:

```bash
docker-compose -f docker-compose.dev.yml up -d
```

This command will:

- Build the Docker image for the application
- Start the application container

The build process may take a significant amount of time (20-30 minutes) as it installs all the required Python dependencies, which are quite extensive for this ML project.

### 6. Verify the Setup

Check that both containers are running:

```bash
docker ps
```

You should see both the Redis container (`ml-model-redis`) and the application container (`ml-models-app`) running.

### 7. Access the Application

Once the application is fully initialized, you can access it at:

- Main application: http://localhost:5001
- API documentation: http://localhost:5001/docs or http://127.0.0.1:5001/docs

You can verify the application is running using curl:

```bash
# Check if the API documentation is accessible
curl -v http://localhost:5001/docs

# Check the OpenAPI specification
curl -v http://localhost:5001/openapi.json
```

If the curl commands return successful responses but you can't access the URLs in your browser, try:

- Using a different browser
- Using 127.0.0.1 instead of localhost
- Checking browser extensions or security settings that might be blocking the connection

## Available API Endpoints

The application exposes several API endpoints:

- `/api/package-material-classifier-simplified` - Classifies package materials from images
- `/api/chemical-prediction` - Predicts chemical properties
- `/api/emissions-factor-matching` - Matches emissions factors
- `/api/product-category-prediction` - Predicts product categories
- `/api/product-manufacturing` - Manufacturing-related predictions
- `/api/file-extraction` - Extracts data from files

### Making API Requests

You can make requests to the API endpoints using curl, Postman, or any HTTP client. For example:

```bash
# Example API request using curl
curl -X POST "http://localhost:5001/api/emissions-factor-matching" \
  -H "Content-Type: application/json" \
  -d '{"query": "your query here"}'
```

For detailed API documentation and interactive testing, use the Swagger UI at http://localhost:5001/docs.

## Troubleshooting

### Container Not Starting

If the container doesn't start, check the logs:

```bash
docker logs ml-models-app
```

### Redis Connection Issues

If the application can't connect to Redis, check the following:

1. Make sure both containers are running in the same Docker network:

   ```bash
   docker network inspect ml_models_ml-models-network
   ```

2. Verify that the Redis container is accessible from the ML models app:

   ```bash
   docker exec ml-models-app ping ml-model-redis
   ```

3. Check that the Redis URL is correctly set to use the container name:

   ```bash
   docker exec ml-models-app env | grep REDIS_URL
   ```

   It should be `redis://ml-model-redis:6379`

4. Test the Redis connection directly:
   ```bash
   docker exec ml-models-app python -c "import redis; r = redis.from_url('redis://ml-model-redis:6379'); print(r.ping())"
   ```
   This should return `True` if the connection is working.

### Long Initialization Time

The application may take some time to initialize as it downloads and loads various ML models. Be patient during the first startup.

### DNS Resolution Issues

If the application is having trouble connecting to external services like Hugging Face, it might be due to DNS resolution issues in Docker. You can add Google's DNS servers to the Docker Compose configuration:

```yaml
app:
  # other configuration...
  dns:
    - *******
    - *******
```

This is already included in the provided docker-compose.yml file.

## Stopping the Application

To stop the application:

```bash
docker-compose -f docker-compose.dev.yml down
```

To stop just the application container:

```bash
docker stop ml-models-app
```

To stop the Redis container:

```bash
docker stop ml-model-redis
```

## Daily Startup Procedures

When you need to restart the application (e.g., after shutting down your computer or stopping the containers), follow these steps:

### 1. Start Docker Desktop

Ensure Docker Desktop is running on your machine.

### 2. Start the Services with Docker Compose

Docker Compose will handle starting both the Redis container and the ML models app. For development, use the development configuration:

```bash
docker-compose -f docker-compose.dev.yml up -d
```

This will start both services in the correct order, with Redis starting first and the ML models app depending on it. The development configuration includes volume mounting to allow code changes without rebuilding the container.

### 3. Verify the Services are Running

```bash
docker ps
```

You should see both the Redis container (`ml-model-redis`) and the application container (`ml-models-app`) in the list of running containers.

## Development vs Production Docker Setup

The repository includes two Docker Compose configurations:

1. **docker-compose.yml** - Standard configuration for production-like environments
2. **docker-compose.dev.yml** - Development configuration with volume mounting

### Benefits of Using docker-compose.dev.yml for Development

- **Live Code Reloading**: Changes to your local code are immediately reflected in the container without rebuilding
- **Faster Iteration**: No need to rebuild the Docker image after code changes
- **Easier Debugging**: Local code changes can be tested immediately
- **Preserved Data**: The Redis container data persists between restarts

The main difference is that docker-compose.dev.yml mounts your local codebase into the container:

```yaml
volumes:
  # Mount the local codebase to the container's working directory
  - ./:/home/<USER>/app
  # Exclude the virtual environment directory if you have one locally
  - /home/<USER>/app/venv
```

This allows you to edit code locally and see changes immediately in the running container.

## Setup Summary

### One-Time Setup Tasks

- Clone the repository
- Create .env file with credentials
- Initial build of the application image

### Daily Startup Tasks

- Start Docker Desktop
- Start both services with Docker Compose

```bash
docker-compose -f docker-compose.dev.yml up -d
```

- Verify containers are running

```bash
docker ps
```

- Check Redis connectivity if needed

```bash
docker exec ml-models-app python -c "import redis; r = redis.from_url('redis://ml-model-redis:6379'); print(r.ping())"
```
