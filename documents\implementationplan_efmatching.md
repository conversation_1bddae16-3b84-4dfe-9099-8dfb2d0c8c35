# Implementation Plan for AI-Assisted EF Matching Service (Phase 1)

This document provides a detailed, step-by-step plan for a coding agent to implement the Phase 1 AI-assisted Emission Factor (EF) Matching Service. It assumes familiarity with the existing codebase structure and aims to minimize ambiguity.

---

## 1. Overview & Objectives

**Goal:** Build a standalone service (`/ai/ef-match/phase1/recommendations`) that:
- Accepts contextual inputs (primary/secondary queries, lifecycle stage, geography, units, category)
- Runs a multi-phase pipeline: Input Category Prediction → Modifier Spotting → ISIC Classification → Query Augmentation → Dynamic Filter Construction → Vector DB Search → LLM Re-ranking → Geography Matching → Final Response
- Returns a structured JSON response with the top EF match, alternatives, confidence, explanation, and processing summary
- Is fully covered by unit and integration tests

**Key deliverables:**
1. `implementationplan.md` (this file)
2. New or modified code in: `emissions_factor_matching/`, `chemical_prediction/`, `shared_predictions.py`, `prompt.py`, `api.py` under `chemical_prediction` and root
3. New API endpoint in `main.py` or in a new router
4. Updated Docker and dependency configuration
5. Tests to validate each phase and end-to-end behavior

---

## 2. High-Level Architecture

```text
Client (web/backend) → AI EF Match Service (Phase 1)
    ├─ Phase 1.0: Input normalization & context setup
    ├─ Phase 1.1: Enhanced Input Category Prediction (LLM)
    ├─ Phase 1.2: Modifier Spotting (LLM)
    ├─ Phase 1.3: ISIC Class Prediction (LLM or logic)
    ├─ Phase 1.4: Query Text Augmentation (LLM)
    ├─ Phase 1.5: Dynamic Filter Construction (code)
    ├─ Phase 1.6: ChromaDB Vector Search (embedding + filters)
    ├─ Phase 1.7: LLM Re-ranking & Justification
    ├─ Phase 1.8: Geography Matching & Record Retrieval
    └─ Phase 1.9: Response Assembly
```

Each phase is implemented as a separate function or class, orchestrated by a top-level service handler.

---

## 3. Project Structure & Files

- `emissions_factor_matching/`
  - **api.py**: HTTP router, request/response Pydantic models, error handling
  - **predictions.py**: orchestration logic; import and call each phase module
  - **model.py**: definitions for internal data structures (e.g., `Candidate`, `MatchedEF`)
  - **dataset.py**: ChromaDB wrappers (embed, query with filters)
  - **geography.py**: geography matching logic
  - **prompt.py**: all LLM prompt templates for category prediction, modifier spotting, ISIC, augmentation, re-ranking
  - **tests/**: unit tests for each module and integration tests for the endpoint

- `shared_predictions.py`: common LLM-calling utilities (e.g., `call_llm`, `parse_response`)
- `chemical_prediction/`: may be extended for input category and modifier models if shared
- `main.py`: application entrypoint; register the new router
- `Dockerfile`, `docker-compose*.yml`: ensure new dependencies installed (e.g., `chromadb`, `openai` or other LLM SDKs)
- `documents/implementationplan.md`: this file

---

## 4. Detailed Implementation Tasks

### 4.1. Phase 0: Input Normalization

1. **Location:** `emissions_factor_matching/api.py` or a new module `input_validation.py`
2. **Tasks:**
   - Define or extend Pydantic request model:
     - `user_query_primary: str`
     - `user_query_secondary: Optional[str] = None`
     - `lca_lifecycle_stage: str`
     - `iso_code: str`
     - `valid_units: List[str]`
     - `product_category_context: Optional[str] = None`
   - Implement a normalization helper:
     - Lowercase trimming, whitespace collapse
   - Validate required fields and allowed `lca_lifecycle_stage` values

### 4.2. Phase 1.1: Enhanced Input Category Prediction

1. **Location:** `emissions_factor_matching/predictions.py` or new file `input_category.py`
2. **Tasks:**
   - Implement `predict_input_category(request_model) -> str`
   - Retrieve a prompt template from `prompt.py`
   - Call `shared_predictions.call_llm(prompt, **kwargs)` and parse output
   - Return a canonical category (e.g., `SERVICE_TRANSPORT_ROAD_FREIGHT`)
   - Unit tests: mock LLM output for known queries

### 4.3. Phase 1.2: Modifier Spotting

1. **Location:** `emissions_factor_matching/predictions.py` or new module `modifier_spotter.py`
2. **Tasks:**
   - Implement `spot_modifiers(request_model, input_category) -> List[str]`
   - Use LLM prompts from `prompt.py` to extract key-value pairs or flat modifiers
   - Return a list of modifiers (e.g., `["diesel", ">32t"]`)
   - Unit tests: assert extraction logic on synthetic examples

### 4.4. Phase 1.3: ISIC Classification Prediction

1. **Location:** `emissions_factor_matching/predictions.py` or new module `isic_prediction.py`
2. **Tasks:**
   - Implement `predict_isic_classes(input_category, modifiers) -> List[str]`
   - Option A: LLM call with prompt
   - Option B: Simple mapping logic (e.g., if category contains `TRANSPORT_ROAD`, return `["4923"]`)
   - Return one or more 4-digit codes
   - Unit tests for mapping logic and mocked LLM

### 4.5. Phase 1.4: Query Text Augmentation

1. **Location:** `emissions_factor_matching/predictions.py` or new module `query_augmenter.py`
2. **Tasks:**
   - Implement `augment_query_text(request_model, input_category, modifiers) -> str`
   - Call LLM with a prompt template for augmentation
   - Return descriptive query text for embedding
   - Unit tests: verify prompt construction and output parsing

### 4.6. Phase 1.5: Dynamic Filter Construction

1. **Location:** `emissions_factor_matching/predictions.py` or new module `filter_builder.py`
2. **Tasks:**
   - Implement `build_filters(lca_stage, input_category, isic_codes, modifiers, iso_code, valid_units) -> dict`
   - Construct JSON-serializable ChromaDB `where` clause:
     - `activity_type` (list)
     - `ISIC Classification` regex
     - `unit` in valid_units
     - transport-specific metadata if available
   - Unit tests: combination of parameters yields expected filter dict

### 4.7. Phase 1.6: Vector DB Search

1. **Location:** `emissions_factor_matching/dataset.py`
2. **Tasks:**
   - Implement `search_candidates(augmented_query, filters) -> List[Candidate]`
   - Use ChromaDB client to embed and query collection
   - Return top N candidates with metadata
   - Integration test: against a test SQLite ChromaDB fixture

### 4.8. Phase 1.7: LLM Re-ranking & Justification

1. **Location:** `emissions_factor_matching/predictions.py` or new module `re_ranker.py`
2. **Tasks:**
   - Implement `re_rank_candidates(original_request, candidates, augmented_query, filters, isic) -> (MatchedEF, List[MatchedEF])`
   - Prompt LLM with full context + candidate metadata
   - Parse out selected EF, confidence, explanation, and alternatives
   - Unit tests: mock candidate list and LLM output

### 4.9. Phase 1.8: Geography Matching & Record Retrieval

1. **Location:** `emissions_factor_matching/geography.py`
2. **Tasks:**
   - Implement `match_geography(matched_entity, iso_code) -> MatchedEF`
   - Query `efs_with_geographies` DataFrame or DB via `dataset.py`
   - Unit tests: known geography variants

### 4.10. Phase 1.9: Response Assembly & API Endpoint

1. **Location:** `emissions_factor_matching/api.py` + `main.py`
2. **Tasks:**
   - In `api.py`, define a POST route `/ai/ef-match/phase1/recommendations`
   - Accept request model, call orchestrator in `predictions.py`
   - Return the Response model including `system_processing_summary`
   - In `main.py`, register the new router
   - End-to-end integration tests in `emissions_factor_matching/tests/`

---

## 5. Testing Strategy

1. **Unit Tests**
   - One test file per module (`test_input_category.py`, `test_modifier_spotting.py`, etc.)
   - Use pytest fixtures and monkeypatch for LLM calls
2. **Integration Tests**
   - Spin up a test ChromaDB instance (in-memory SQLite) populated with a minimal EF dataset
   - Call the real endpoint via TestClient and assert JSON keys & types
3. **Performance & Cost**
   - Benchmark average latency across phases
   - Log LLM call counts and durations

---

## 6. Deployment & Dependencies

- **Dockerfile**: add Python packages (`chromadb`, `openai` or chosen LLM SDK, `pydantic`, `fastapi`)
- **docker-compose.dev.yml**: add service for ChromaDB if needed
- Update `requirements.txt` or `pyproject.toml`

---

## 7. Timeline & Milestones

| Week | Milestone                                          |
|------|----------------------------------------------------|
| 1    | Phase 0–1.3: Input models, category, modifiers, ISIC|
| 2    | Phase 1.4–1.6: Query augmentation, filters, search  |
| 3    | Phase 1.7–1.8: Re-ranking, geography matching       |
| 4    | Phase 1.9: API endpoint, end-to-end tests          |
| 5    | Integration tests, performance tuning, docs       |

---

*Prepared on May 26, 2025*  
*For any questions, refer to existing documents in `/documents`: ARCHITECTURE.md, CHROMA_QUERY_README.md, JSON.md.*
