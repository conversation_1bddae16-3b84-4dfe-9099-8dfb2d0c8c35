from unittest import TestCase
from chemical_prediction.predictions import predict_material_classification

validation_classifications = [
    ("PVC", "Plastic"),
    ("PETE", "Plastic"),
    ("HDPE", "Plastic"),
    ("LDPE", "Plastic"),
    ("Cardstock", "Paper"),
    ("Kraft Paper", "Paper"),
    ("Aluminum Foil", "Aluminium"),
    ("Steel Sheet", "Steel"),
    ("Glass Bottle", "Glass"),
    ("Wood Plank", "Wood"),
    ("Copper Wire", "NONE"),
]

class TestMaterialClassificationPrediction(TestCase):
    def test_mateiral_classification_predictions(self):
        for material, validation_classification in validation_classifications:
            classification = predict_material_classification(material)
            self.assertEqual(classification, validation_classification)