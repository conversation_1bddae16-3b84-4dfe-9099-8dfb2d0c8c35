from unittest import IsolatedAsyncioTestCase
from chemical_prediction.predictions import get_valid_eol_product_category

validation_true_categories = [
    #"Makeup wipes",
    "LED Lightbulbs",
    "Backpacks",
    "Batteries",
    "dryer sheets",
    "fabric softner dryer sheets",
    "dryer balls",
    "False Eyelashes",
    "Travel Pouches",
    "Office Chairs",
    "Coffee grounds",
    "Shopping Totes",
    "Basket"
]

validation_false_categories = [
    "Laundry detergent",
    "Makeup",
    "Multi-surface cleaner",
    "Glass cleaner",
    #"bar soap",
    "laundry detergent tablet",
    "dishwashing powder",
    "lotion and moisturizer",
    "anti-aging skin care kits",
    "Cleaning Powder",
    "Body Oil",
    "Face Oil",
    "Facial Cleansers",
    "Lotion & Moisturizer",
]



class TestHasEOLProductCategory(IsolatedAsyncioTestCase):
    async def test_has_eol_product_category(self):
        for product_category in validation_true_categories:
            has_eol = await get_valid_eol_product_category(product_category)
            self.assertEqual(has_eol, True)

        for product_category in validation_false_categories:
            has_eol = await get_valid_eol_product_category(product_category)
            self.assertEqual(has_eol, False)
