from unittest import IsolatedAsyncioTestCase
from emissions_factor_matching.predictions import get_technological_representation


query_validations = [
    (("sodium chloride", "sodium chloride production"), ["All technology categories are similar"]),
    (("steel", "steel production, unalloyed"), ["Two of the technology categories are similar", "Three of the technology categories are similar"]),
    (("nylon fabric", "nylon 6 production"), ["Two of the technology categories are similar", "Three of the technology categories are similar"]),
]


class TestTechRepresentation(IsolatedAsyncioTestCase):
    async def test_get_tech_representation(self):
        for query, validation in query_validations:
            (
                process_name,
                activity_name
            ) = query

            representation = await get_technological_representation(
                process_name,
                activity_name,
            )

            self.assertTrue(representation in validation)
