"""
Test script to validate LangChain's Azure OpenAI integration with structured output.
This script tests the migration feasibility before implementing across the codebase.
"""

import os
import asyncio
from typing import List, Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Test Pydantic models similar to what's used in the codebase
class ChemicalInfo(BaseModel):
    """Test model for chemical information extraction"""
    chemical_name: str = Field(description="The name of the chemical")
    cas_number: Optional[str] = Field(description="CAS number in format xxxxxx-xx-x or NONE")
    is_chemical: bool = Field(description="Whether the input is actually a chemical")
    synonyms: List[str] = Field(default_factory=list, description="Common chemical synonyms")

class ProductCategory(BaseModel):
    """Test model for product categorization"""
    product_name: str = Field(description="The product name")
    category: str = Field(description="The predicted category")
    confidence: float = Field(description="Confidence score between 0 and 1")
    reasoning: str = Field(description="Explanation for the categorization")

class EmissionFactorMatch(BaseModel):
    """Test model for emission factor matching"""
    activity_uuid: str = Field(description="The UUID of the matched activity")
    confidence: float = Field(description="Confidence score between 0 and 1")
    match_explanation: str = Field(description="Explanation for why this match was selected")

def test_langchain_azure_openai():
    """Test LangChain with Azure OpenAI"""
    try:
        from langchain_openai import AzureChatOpenAI
        from langchain_core.prompts import ChatPromptTemplate
        
        print("[OK] Successfully imported LangChain modules")
        
        # Initialize Azure OpenAI with LangChain
        llm = AzureChatOpenAI(
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version="2024-08-01-preview",  # Required for structured output
            azure_deployment=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
            temperature=0,
        )
        print("[OK] Successfully initialized AzureChatOpenAI")
        
        # Test 1: Chemical Information Extraction
        print("\n[TEST] Test 1: Chemical Information Extraction")
        chemical_llm = llm.with_structured_output(ChemicalInfo)
        
        chemical_prompt = ChatPromptTemplate.from_messages([
            ("system", "You are a chemical expert. Extract information about the given chemical."),
            ("user", "Analyze this substance: {substance}")
        ])
        
        chemical_chain = chemical_prompt | chemical_llm
        
        # Test with a real chemical
        result1 = chemical_chain.invoke({"substance": "Sodium Chloride"})
        print(f"Result 1: {result1}")
        print(f"Type: {type(result1)}")
        assert isinstance(result1, ChemicalInfo)
        assert result1.cas_number == "7647-14-5"
        
        # Test with a non-chemical
        result2 = chemical_chain.invoke({"substance": "Coffee"})
        print(f"Result 2: {result2}")
        assert result2.cas_number == "NONE" or result2.cas_number is None
        
        print("[OK] Chemical extraction test passed!")
        
        # Test 2: Product Categorization
        print("\n[TEST] Test 2: Product Categorization")
        product_llm = llm.with_structured_output(ProductCategory)
        
        product_prompt = ChatPromptTemplate.from_messages([
            ("system", "You are a product categorization expert. Categorize the given product."),
            ("user", "Categorize this product: {product}")
        ])
        
        product_chain = product_prompt | product_llm
        
        result3 = product_chain.invoke({"product": "Organic Fair Trade Dark Chocolate 70% Cocoa"})
        print(f"Result 3: {result3}")
        assert isinstance(result3, ProductCategory)
        assert 0 <= result3.confidence <= 1
        
        print("[OK] Product categorization test passed!")
        
        # Test 3: Complex nested structure
        print("\n[TEST] Test 3: Emission Factor Matching")
        ef_llm = llm.with_structured_output(EmissionFactorMatch)
        
        ef_prompt = ChatPromptTemplate.from_messages([
            ("system", "Match the input to an emission factor. Return a UUID, confidence, and explanation."),
            ("user", "Match this activity: {activity}")
        ])
        
        ef_chain = ef_prompt | ef_llm
        
        result4 = ef_chain.invoke({"activity": "Transportation of goods by truck, 10 tons, 500km"})
        print(f"Result 4: {result4}")
        assert isinstance(result4, EmissionFactorMatch)
        
        print("[OK] Emission factor matching test passed!")
        
        return True
        
    except ImportError as e:
        print(f"[ERROR] Import Error: {e}")
        print("Please install: pip install langchain-openai langchain-core")
        return False
    except Exception as e:
        print(f"[ERROR] Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_langchain():
    """Test async implementation"""
    try:
        from langchain_openai import AzureChatOpenAI
        from langchain_core.prompts import ChatPromptTemplate
        
        print("\n[TEST] Testing Async Implementation")
        
        # Initialize Azure OpenAI with LangChain
        llm = AzureChatOpenAI(
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version="2024-08-01-preview",  # Required for structured output
            azure_deployment=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
            temperature=0,
        )
        
        chemical_llm = llm.with_structured_output(ChemicalInfo)
        
        chemical_prompt = ChatPromptTemplate.from_messages([
            ("system", "You are a chemical expert. Extract information about the given chemical."),
            ("user", "Analyze this substance: {substance}")
        ])
        
        chemical_chain = chemical_prompt | chemical_llm
        
        # Async invocation
        result = await chemical_chain.ainvoke({"substance": "Water (H2O)"})
        print(f"Async Result: {result}")
        assert isinstance(result, ChemicalInfo)
        
        print("[OK] Async test passed!")
        return True
        
    except Exception as e:
        print(f"[ERROR] Async Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_current_implementation():
    """Compare with current OpenAI implementation"""
    print("\n=== Comparing with current implementation ===")
    
    try:
        from openai import AzureOpenAI
        import json
        
        # Current implementation
        client = AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version="2024-08-01-preview",  # Required for structured output
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
        )
        
        messages = [
            {
                "role": "system",
                "content": "Extract chemical information. Return JSON with fields: chemical_name, cas_number, is_chemical, synonyms"
            },
            {
                "role": "user", 
                "content": "Analyze: Sodium Chloride"
            }
        ]
        
        response = client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
            messages=messages,
            temperature=0,
            response_format={"type": "json_object"}
        )
        
        current_result = response.choices[0].message.content
        print(f"Current implementation result: {current_result}")
        
        # Parse and validate
        parsed = json.loads(current_result)
        print(f"Parsed successfully: {parsed}")
        
        print("[OK] Current implementation works but requires manual parsing")
        
    except Exception as e:
        print(f"Current implementation error: {e}")

if __name__ == "__main__":
    print("=== Testing LangChain Azure OpenAI with Structured Output ===\n")
    
    # Check environment variables
    required_vars = ["AZURE_OPENAI_API_KEY", "AZURE_OPENAI_ENDPOINT"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"[ERROR] Missing environment variables: {missing_vars}")
        print("Please ensure .env file contains all required Azure OpenAI credentials")
        exit(1)
    
    print("[OK] Environment variables loaded successfully")
    
    # Run tests
    sync_success = test_langchain_azure_openai()
    
    if sync_success:
        # Run async test
        asyncio.run(test_async_langchain())
        
        # Compare with current implementation
        compare_with_current_implementation()
        
        print("\n[OK] All tests completed! LangChain migration is feasible.")
    else:
        print("\n[ERROR] Tests failed. Please check the errors above.")