name: Build & Push Docker image to GHCR

on:
  workflow_call:
    inputs:
      tag_name:
        description: 'Tag for the Docker images'
        required: true
        type: string
      slot_name:
        description: 'Azure deployment slot name'
        required: false
        type: string
    secrets:
      WORKFLOW_GITHUB_TOKEN:
        description: 'GitHub token for authentication'
        required: true
      AZURE_PUBLISH_PROFILE:
        description: 'Azure publish profile'
        required: true


env:
  REGISTRY: ghcr.io
  APP_IMAGE_NAME: carbonbright/ml_models
  AZURE_WEBAPP_NAME: ml-models-api

jobs:
  build-and-push:
    runs-on: ubuntu-22.04

    permissions:
      contents: read
      packages: write

    steps:
      - name: Check out the repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.WORKFLOW_GITHUB_TOKEN }}

      - name: Build and Push App Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.APP_IMAGE_NAME }}:${{ inputs.tag_name }}
          cache-from: type=registry,ref=${{ env.REGISTRY }}/${{ env.APP_IMAGE_NAME }}:buildcache
          cache-to: type=registry,ref=${{ env.REGISTRY }}/${{ env.APP_IMAGE_NAME }}:buildcache,mode=max

  deploy:
    runs-on: ubuntu-22.04
    needs: build-and-push

    steps:
      - name: Deploy to Azure Web App
        uses: azure/webapps-deploy@v2
        with:
          app-name: ${{ env.AZURE_WEBAPP_NAME }}
          publish-profile: ${{ secrets.AZURE_PUBLISH_PROFILE }}
          images: ${{ env.REGISTRY }}/${{ env.APP_IMAGE_NAME }}:${{ inputs.tag_name }}
          slot-name: ${{ inputs.slot_name }}
