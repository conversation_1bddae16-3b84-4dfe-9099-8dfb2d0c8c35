# PowerShell script to run ChromaDB queries in the existing ml-models-app container

param (
    [string]$query_text = "",
    [string]$where_clause = "",
    [int]$n_results = 0,
    [string]$collection_name = "",
    [switch]$save_results,
    [string]$output_file = "",
    [int]$display_count = 0
)

Write-Host "Running ChromaDB Query in existing ml-models-app container..." -ForegroundColor Green

# Initialize docker command
$dockerArgs = @("exec", "-it", "ml-models-app", "python", "/home/<USER>/app/query_chroma_existing_collection.py")

# Check if named parameters were provided
$hasNamedParams = $false
$paramDisplay = @()

# Add parameters to docker command if they were provided
if ($query_text) {
    $dockerArgs += "--query_text"
    $dockerArgs += """$query_text"""
    $paramDisplay += "query_text: '$query_text'"
    $hasNamedParams = $true
}

if ($where_clause) {
    # Preserve the where_clause exactly as provided
    $dockerArgs += "--where_clause"
    $dockerArgs += $where_clause
    $paramDisplay += "where_clause: $where_clause"
    $hasNamedParams = $true
}

if ($n_results -gt 0) {
    $dockerArgs += "--n_results"
    $dockerArgs += "$n_results"
    $paramDisplay += "n_results: $n_results"
    $hasNamedParams = $true
}

if ($collection_name) {
    $dockerArgs += "--collection_name"
    $dockerArgs += "$collection_name"
    $paramDisplay += "collection_name: $collection_name"
    $hasNamedParams = $true
}

if ($save_results) {
    $dockerArgs += "--save_results"
    $paramDisplay += "save_results: true"
    $hasNamedParams = $true
}

if ($output_file) {
    $dockerArgs += "--output_file"
    $dockerArgs += """$output_file"""
    $paramDisplay += "output_file: '$output_file'"
    $hasNamedParams = $true
}

if ($display_count -gt 0) {
    $dockerArgs += "--display_count"
    $dockerArgs += "$display_count"
    $paramDisplay += "display_count: $display_count"
    $hasNamedParams = $true
}

# If no named parameters were provided but there are arguments, pass them directly
if (-not $hasNamedParams -and $args.Count -gt 0) {
    # Add all script arguments to the docker command
    foreach ($arg in $args) {
        if ($arg -match "\s") {
            # If argument contains spaces, add quotes
            $dockerArgs += """$arg"""
        } else {
            $dockerArgs += $arg
        }
    }

    # Display the command being run
    $cmdDisplay = $args -join " "
    Write-Host "Running with custom query (legacy mode): $cmdDisplay" -ForegroundColor Yellow
}
elseif (-not $hasNamedParams) {
    # No parameters at all, run with defaults
    Write-Host "No custom query provided. Running with default parameters..." -ForegroundColor Yellow
}
else {
    # Display the parameters being used
    Write-Host "Running with parameters:" -ForegroundColor Yellow
    foreach ($param in $paramDisplay) {
        Write-Host "  $param" -ForegroundColor Cyan
    }
}

# Execute the docker command with all arguments
& docker $dockerArgs

Write-Host "Done!" -ForegroundColor Green
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
