from typing import Literal
from pydantic import BaseModel, Field
from completions import get_chat_completion, get_structured_completion
from config import config
from clients import get_phi_client
from completions import get_azure_completion
from completion_utils import parse_template_response
from utils.strings import make_xml_safe


class EOLProductCategoryResponse(BaseModel):
    """Response model for EOL product category validation"""
    explanation: str = Field(description="Explanation of why the product is or isn't EOL eligible")
    is_eol_eligible: bool = Field(description="Whether the product is NOT a liquid, soluble, or topical product")


class HSCodeResponse(BaseModel):
    """Response model for HS code prediction"""
    hs_code: str = Field(description="The harmonized system code for the chemical")


class MaterialClassificationResponse(BaseModel):
    """Response model for material classification"""
    classification: Literal['Plastic', 'Paper', 'Cardboard', 'Aluminium', 'Glass', 'Steel', 'Wood', 'NONE'] = Field(
        description="The material classification"
    )


async def get_valid_eol_product_category(product_category: str) -> bool:
    """
    Determine if a product category is valid for EOL (End of Life) processing.
    Returns True if the product is NOT a liquid, soluble, or topical product.
    
    Currently still uses Phi-3 with XML parsing for compatibility.
    """
    messages = [
        {
            "role": "system",
            "content": (
                "Determine whether '{product_category}' is any of the following: a liquid (or liquid formulation i.e detergent, surface cleaner, soap, etc.), solubles (dissolves in water i.e powder detergent, bar soap, etc.), or topical product (applied to body i.e makeup, lotion, etc.)."
                "Output format <RESPONSE><EXPLANATION>EXPLANATION</EXPLANATION><ANSWER>boolean</ANSWER></RESPONSE>"
            )
        },
        {
            "role": "user",
            "content": f"Product Category: '{make_xml_safe(product_category)}'",
        }
    ]
    
    # Still using Phi-3 for this specific use case
    async with get_phi_client() as phi_client:
        completion = await get_azure_completion(phi_client, messages)
    
    response_dict = parse_template_response(completion)
    return response_dict["ANSWER"].strip().lower() == "false"


def predict_hscode(chemical_name: str) -> str:
    """
    Predict the Harmonized System (HS) code for a chemical.
    Now uses structured output with LangChain.
    """
    messages = [
        {
            "role": "system",
            "content": (
                "Given a chemical, return the harmonized system (HS) code.\n"
                "The HS code should be a valid code used in international trade.\n"
                "Example:\n"
                "Chemical: 'Water' -> HS Code: '22019000'"
            )
        },
        {
            "role": "user",
            "content": f"Chemical: '{chemical_name}'",
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=HSCodeResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    return result.hs_code if result else ""


def predict_material_classification(material: str) -> str:
    """
    Predict the material classification for a given material.
    Now uses structured output with LangChain.
    """
    messages = [
        {
            "role": "system",
            "content": (
                "Given a material, classify it into one of the following categories:\n"
                "- Plastic: Any plastic-based material (PET, HDPE, PVC, etc.)\n"
                "- Paper: Paper-based materials\n"
                "- Cardboard: Cardboard or corrugated materials\n"
                "- Aluminium: Aluminium-based materials\n"
                "- Glass: Glass-based materials\n"
                "- Steel: Steel or iron-based materials\n"
                "- Wood: Wood-based materials\n"
                "- NONE: If the material doesn't fit any category\n\n"
                "Examples:\n"
                "Material: 'PET' -> Classification: 'Plastic'\n"
                "Material: 'Cardstock' -> Classification: 'Paper'\n"
                "Material: 'Brass Clamp' -> Classification: 'NONE'"
            )
        },
        {
            "role": "user",
            "content": f"Material: '{material}'",
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=MaterialClassificationResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    return result.classification if result else "NONE"