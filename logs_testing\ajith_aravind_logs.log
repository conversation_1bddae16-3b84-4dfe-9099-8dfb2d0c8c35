2025-05-22 18:29:34.349 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 18:29:34.486 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:29:38.771 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 4287.72ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:29:38.773 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:29:38.805 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:29:38.822 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:29:39.988 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1166.42ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:29:39.989 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:29:39.993 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:29:40.000 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:29:40.011 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:29:40.017 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:29:41.882 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1865.30ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 18:29:41.884 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:29:41.888 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:29:41.893 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:29:41.901 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:29:41.903 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:29:42.694 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 791.50ms, model=gpt-4o, tokensΓëê44+12
2025-05-22 18:29:42.696 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: Primary material: Hexafluoroethane-based polymer.
2025-05-22 18:29:42.701 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'Primary material: Hexafluoroethane-based polymer.'
2025-05-22 18:29:42.705 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'Primary material: Hexafluoroethane-based polymer.'
2025-05-22 18:29:42.706 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'Primary material: Hexafluoroethane-based polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:29:42.707 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:29:42.709 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:29:43.485 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 776.08ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:29:43.487 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:29:43.492 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:29:43.495 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:29:44.258 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 762.70ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:29:44.260 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:29:44.263 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:29:44.266 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:29:44.271 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:29:44.272 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:29:44.274 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'Primary material: Hexafluoroethane-based polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:30:43.438 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 59163.08ms, returned 125 results
2025-05-22 18:30:43.439 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, flame retardant', 'polyurethane production, flexible foam, TDI-based, high density']
2025-05-22 18:30:43.440 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.24572835862636566, 0.24572835862636566, 0.26604539155960083, 0.278313547372818, 0.2791008949279785]
2025-05-22 18:30:43.441 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:30:43.443 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'Primary material: Hexafluoroethane-based polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:30:43.519 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:30:47.692 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 4172.95ms, model=gpt-4o, tokensΓëê23796+123
2025-05-22 18:30:47.694 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:30:47.954 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:30:49.166 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:30:49.191 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:35:21.303 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 18:35:21.355 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:35:24.463 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 3108.29ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:35:24.464 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:35:24.482 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:35:24.491 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:35:25.501 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1009.77ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:35:25.503 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:35:25.505 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:35:25.507 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:35:25.515 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:35:25.517 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:35:27.237 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1720.09ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 18:35:27.239 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:35:27.243 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:35:27.245 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:35:27.247 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:35:27.249 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:35:28.622 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1373.45ms, model=gpt-4o, tokensΓëê44+15
2025-05-22 18:35:28.624 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is a **hexafluoroethane-based polymer**.
2025-05-22 18:35:28.628 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:35:28.631 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:35:28.632 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:35:28.637 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:35:28.642 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:35:29.746 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1104.28ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:35:29.748 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:35:29.752 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:35:29.755 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:35:30.565 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 809.98ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:35:30.566 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:35:30.567 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:35:30.568 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:35:30.569 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:35:30.570 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:35:30.572 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:25.106 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 54532.52ms, returned 125 results
2025-05-22 18:36:25.109 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, high density', 'polyurethane production, flexible foam, TDI-based, flame retardant']
2025-05-22 18:36:25.110 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.25423571467399597, 0.25423571467399597, 0.2608909606933594, 0.2712603807449341, 0.2743716239929199]
2025-05-22 18:36:25.111 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:36:25.112 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:25.175 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:36:30.215 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 5040.79ms, model=gpt-4o, tokensΓëê23751+96
2025-05-22 18:36:30.218 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:36:30.477 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:36:31.197 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:36:31.205 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:36:34.027 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 18:36:34.029 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:36:36.168 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 2138.81ms, model=gpt-4o, tokensΓëê118+3
2025-05-22 18:36:36.175 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: CAS No.: NONE
2025-05-22 18:36:36.177 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:36:36.179 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:36:37.478 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1298.81ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:36:37.480 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:36:37.483 | WARNING  | completions:get_chat_completion:279 - ajith_aravind Validation #1 did not match chat completion, returning None
2025-05-22 18:36:37.485 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: None
2025-05-22 18:36:37.486 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:37.489 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:36:39.686 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 2197.09ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 18:36:39.688 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:36:39.690 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:36:39.692 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:36:39.693 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:39.696 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:36:42.256 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 2559.87ms, model=gpt-4o, tokensΓëê44+15
2025-05-22 18:36:42.258 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is a **hexafluoroethane-based polymer**.
2025-05-22 18:36:42.260 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:36:42.262 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:36:42.263 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:42.267 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:42.270 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:36:42.990 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 719.98ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:36:42.992 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:36:42.994 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:36:42.997 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:36:43.803 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 806.63ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:36:43.805 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:36:43.806 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:36:43.807 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:36:43.808 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:36:43.809 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:36:43.810 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:45.775 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 1964.11ms, returned 125 results
2025-05-22 18:36:45.776 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, high density', 'polyurethane production, flexible foam, TDI-based, flame retardant']
2025-05-22 18:36:45.777 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.25423571467399597, 0.25423571467399597, 0.2608909606933594, 0.2712603807449341, 0.2743716239929199]
2025-05-22 18:36:45.778 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:36:45.779 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:45.803 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:36:50.012 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 4208.58ms, model=gpt-4o, tokensΓëê23751+97
2025-05-22 18:36:50.014 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:36:50.266 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:36:50.277 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:36:50.289 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:36:54.386 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 18:36:54.388 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:36:56.380 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1991.82ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:36:56.381 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:36:56.382 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:36:56.384 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:36:57.247 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 863.27ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:36:57.249 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:36:57.251 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:36:57.254 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:36:57.255 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:57.257 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:36:58.893 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1635.71ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 18:36:58.895 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:36:58.898 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:36:58.899 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:36:58.902 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:58.905 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:36:59.948 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1042.82ms, model=gpt-4o, tokensΓëê44+15
2025-05-22 18:36:59.950 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is a **hexafluoroethane-based polymer**.
2025-05-22 18:36:59.952 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:36:59.954 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:36:59.958 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:59.961 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:59.964 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:37:00.819 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 855.23ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:37:00.821 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:37:00.823 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:37:00.824 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:37:02.565 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1740.28ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:37:02.567 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:37:02.568 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:37:02.570 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:37:02.572 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:37:02.574 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:37:02.575 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:04.438 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 1861.34ms, returned 125 results
2025-05-22 18:37:04.440 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, high density', 'polyurethane production, flexible foam, TDI-based, flame retardant']
2025-05-22 18:37:04.441 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.25423571467399597, 0.25423571467399597, 0.2608909606933594, 0.2712603807449341, 0.2743716239929199]
2025-05-22 18:37:04.442 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:37:04.443 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:04.472 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:37:10.440 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 5968.39ms, model=gpt-4o, tokensΓëê23751+100
2025-05-22 18:37:10.441 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:37:11.292 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:37:11.305 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:37:11.320 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:37:20.336 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 18:37:20.339 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:37:22.288 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1948.76ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:37:22.290 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:37:22.298 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:37:22.320 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:37:23.337 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1016.30ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:37:23.339 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:37:23.344 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:37:23.414 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:37:23.421 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:23.424 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:37:25.138 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1713.20ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 18:37:25.142 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:37:25.146 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:37:25.150 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:37:25.156 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:25.158 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:37:26.882 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1723.70ms, model=gpt-4o, tokensΓëê44+12
2025-05-22 18:37:26.884 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: Primary material: Hexafluoroethane-based polymer
2025-05-22 18:37:26.888 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'Primary material: Hexafluoroethane-based polymer'
2025-05-22 18:37:26.893 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'Primary material: Hexafluoroethane-based polymer'
2025-05-22 18:37:26.898 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'Primary material: Hexafluoroethane-based polymer Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:26.909 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:26.912 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:37:28.096 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1184.26ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:37:28.099 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:37:28.102 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:37:28.105 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:37:29.262 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1157.27ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:37:29.287 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:37:29.291 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:37:29.294 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:37:29.298 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:37:29.299 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:37:29.300 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'Primary material: Hexafluoroethane-based polymer Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:32.397 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 3095.54ms, returned 125 results
2025-05-22 18:37:32.399 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, rigid foam', 'polyurethane production, flexible foam, TDI-based, high density']
2025-05-22 18:37:32.401 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.25008144974708557, 0.25008144974708557, 0.26972097158432007, 0.2831781208515167, 0.2852359712123871]
2025-05-22 18:37:32.402 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:37:32.404 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'Primary material: Hexafluoroethane-based polymer Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:32.443 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:37:37.812 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 5368.60ms, model=gpt-4o, tokensΓëê23814+109
2025-05-22 18:37:37.814 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:37:38.069 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:37:38.082 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:37:38.093 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:40:53.265 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='steel', iso=GLO, api_version=latest
2025-05-22 18:40:53.267 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:40:56.192 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 2925.05ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:40:56.193 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:40:56.196 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:40:56.198 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:40:56.946 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 748.61ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:40:56.948 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:40:56.952 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:40:56.958 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:40:56.961 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'steel'
2025-05-22 18:40:56.963 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:40:58.369 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1405.75ms, model=gpt-4o, tokensΓëê29+1
2025-05-22 18:40:58.373 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:40:58.376 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:40:58.379 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:40:58.382 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'steel'
2025-05-22 18:40:58.384 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:41:00.079 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1695.61ms, model=gpt-4o, tokensΓëê29+7
2025-05-22 18:41:00.080 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is steel.
2025-05-22 18:41:00.085 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is steel.'
2025-05-22 18:41:00.086 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'steel', constituent: 'The primary material is steel.'
2025-05-22 18:41:00.088 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is steel. steel'
2025-05-22 18:41:00.090 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'steel'
2025-05-22 18:41:00.091 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:01.134 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1043.39ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:01.136 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:01.138 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:41:01.139 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:02.104 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 965.22ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:02.106 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:02.108 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:41:02.110 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:41:02.112 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:41:02.113 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:41:02.115 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is steel. steel'
2025-05-22 18:41:04.746 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 2630.33ms, returned 125 results
2025-05-22 18:41:04.747 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['reinforcing steel production', 'metal working, average for steel product manufacturing', 'steel production, electric, low-alloyed', 'steel production, electric, low-alloyed', 'steel production, chromium steel 18/8, hot rolled']
2025-05-22 18:41:04.748 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.16215269267559052, 0.1647111177444458, 0.21167069673538208, 0.21167069673538208, 0.21939167380332947]
2025-05-22 18:41:04.751 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:41:04.752 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is steel. steel'
2025-05-22 18:41:04.779 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:41:09.037 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 4257.40ms, model=gpt-4o, tokensΓëê22578+96
2025-05-22 18:41:09.038 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:41:09.293 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:41:09.300 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:41:09.310 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:41:11.554 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='steel', iso=GLO, api_version=latest
2025-05-22 18:41:11.558 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:41:13.542 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1984.13ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:41:13.543 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:41:13.545 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:41:13.549 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:41:14.372 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 822.69ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:41:14.373 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:41:14.376 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:41:14.381 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:41:14.382 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'steel'
2025-05-22 18:41:14.384 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:41:15.874 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1489.46ms, model=gpt-4o, tokensΓëê29+1
2025-05-22 18:41:15.875 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:41:15.880 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:41:15.883 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:41:15.888 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'steel'
2025-05-22 18:41:15.889 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:41:16.623 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 733.35ms, model=gpt-4o, tokensΓëê29+11
2025-05-22 18:41:16.625 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: Steel is primarily composed of iron and carbon.
2025-05-22 18:41:16.629 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'Steel is primarily composed of iron and carbon.'
2025-05-22 18:41:16.635 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'steel', constituent: 'Steel is primarily composed of iron and carbon.'
2025-05-22 18:41:16.639 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'Steel is primarily composed of iron and carbon. steel'
2025-05-22 18:41:16.642 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'steel'
2025-05-22 18:41:16.644 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:17.575 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 931.49ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:17.577 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:17.581 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:41:17.586 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:18.263 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 676.84ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:18.266 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:18.269 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:41:18.272 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:41:18.274 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:41:18.276 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:41:18.277 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'Steel is primarily composed of iron and carbon. steel'
2025-05-22 18:41:22.902 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 4624.04ms, returned 125 results
2025-05-22 18:41:22.905 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['metal working, average for steel product manufacturing', 'steel production, electric, low-alloyed', 'steel production, electric, low-alloyed', 'reinforcing steel production', 'steel production, electric, chromium steel 18/8']
2025-05-22 18:41:22.908 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.24956488609313965, 0.2593756914138794, 0.2593756914138794, 0.2608494162559509, 0.2718929350376129]
2025-05-22 18:41:22.910 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:41:22.911 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'Steel is primarily composed of iron and carbon. steel'
2025-05-22 18:41:22.952 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:41:31.015 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 8063.03ms, model=gpt-4o, tokensΓëê22776+102
2025-05-22 18:41:31.018 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:41:31.272 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:41:31.283 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:41:31.294 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:41:35.429 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='steel', iso=GLO, api_version=latest
2025-05-22 18:41:35.430 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:41:37.354 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1924.00ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:41:37.356 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:41:37.357 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:41:37.359 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:41:38.198 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 838.66ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:41:38.200 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:41:38.201 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:41:38.204 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:41:38.207 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'steel'
2025-05-22 18:41:38.209 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:41:39.920 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1710.14ms, model=gpt-4o, tokensΓëê29+1
2025-05-22 18:41:39.922 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:41:39.927 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:41:39.930 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:41:39.945 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'steel'
2025-05-22 18:41:39.947 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:41:40.935 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 988.47ms, model=gpt-4o, tokensΓëê29+7
2025-05-22 18:41:40.937 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is steel.
2025-05-22 18:41:40.939 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is steel.'
2025-05-22 18:41:40.941 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'steel', constituent: 'The primary material is steel.'
2025-05-22 18:41:40.943 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is steel. steel'
2025-05-22 18:41:40.945 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'steel'
2025-05-22 18:41:40.953 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:41.770 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 816.79ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:41.772 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:41.774 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:41:41.776 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:42.889 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1112.37ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:42.892 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:42.894 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:41:42.896 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:41:42.920 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:41:42.924 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:41:42.926 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is steel. steel'
2025-05-22 18:41:44.856 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 1929.24ms, returned 125 results
2025-05-22 18:41:44.858 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['reinforcing steel production', 'metal working, average for steel product manufacturing', 'steel production, electric, low-alloyed', 'steel production, electric, low-alloyed', 'steel production, chromium steel 18/8, hot rolled']
2025-05-22 18:41:44.860 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.16215269267559052, 0.1647111177444458, 0.21167069673538208, 0.21167069673538208, 0.21939167380332947]
2025-05-22 18:41:44.862 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:41:44.864 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is steel. steel'
2025-05-22 18:41:44.901 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:41:50.912 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 6010.83ms, model=gpt-4o, tokensΓëê22578+101
2025-05-22 18:41:50.914 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:41:51.168 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:41:51.177 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:41:51.192 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:41:59.651 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='steel', iso=GLO, api_version=latest
2025-05-22 18:41:59.653 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:42:01.537 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1884.25ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:42:01.539 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:42:01.541 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:42:01.543 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:42:02.474 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 930.56ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:42:02.477 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:42:02.479 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:42:02.483 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:42:02.487 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'steel'
2025-05-22 18:42:02.489 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:42:04.135 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1645.26ms, model=gpt-4o, tokensΓëê29+1
2025-05-22 18:42:04.136 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:42:04.140 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:42:04.149 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:42:04.151 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'steel'
2025-05-22 18:42:04.155 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:42:05.269 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1113.18ms, model=gpt-4o, tokensΓëê29+7
2025-05-22 18:42:05.271 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is steel.
2025-05-22 18:42:05.274 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is steel.'
2025-05-22 18:42:05.277 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'steel', constituent: 'The primary material is steel.'
2025-05-22 18:42:05.278 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is steel. steel'
2025-05-22 18:42:05.281 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'steel'
2025-05-22 18:42:05.283 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:42:06.288 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1004.97ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:42:06.290 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:42:06.293 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:42:06.296 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:42:07.343 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1047.54ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:42:07.345 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:42:07.349 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:42:07.358 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:42:07.362 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:42:07.363 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:42:07.364 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is steel. steel'
2025-05-22 18:42:10.135 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 2769.92ms, returned 125 results
2025-05-22 18:42:10.137 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['reinforcing steel production', 'metal working, average for steel product manufacturing', 'steel production, electric, low-alloyed', 'steel production, electric, low-alloyed', 'steel production, chromium steel 18/8, hot rolled']
2025-05-22 18:42:10.139 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.16215269267559052, 0.1647111177444458, 0.21167069673538208, 0.21167069673538208, 0.21939167380332947]
2025-05-22 18:42:10.141 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:42:10.151 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is steel. steel'
2025-05-22 18:42:10.232 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:42:17.713 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 7480.26ms, model=gpt-4o, tokensΓëê22578+101
2025-05-22 18:42:17.715 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:42:17.972 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:42:17.983 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:42:17.994 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-23 08:07:13.505 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane Polymer XYZ-2025', iso=GLO, api_version=latest
2025-05-23 08:07:13.508 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-23 08:07:14.928 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1420.06ms, model=gpt-4o, tokensΓëê110+1
2025-05-23 08:07:14.929 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-23 08:07:14.933 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-23 08:07:14.938 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-23 08:07:15.789 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 851.29ms, model=gpt-4o, tokensΓëê110+1
2025-05-23 08:07:15.791 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-23 08:07:15.793 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-23 08:07:15.795 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-23 08:07:15.796 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane Polymer XYZ-2025'
2025-05-23 08:07:15.798 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-23 08:07:17.191 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1392.96ms, model=gpt-4o, tokensΓëê36+1
2025-05-23 08:07:17.192 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-23 08:07:17.196 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-23 08:07:17.199 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-23 08:07:17.200 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane Polymer XYZ-2025'
2025-05-23 08:07:17.201 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-23 08:07:18.658 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1456.79ms, model=gpt-4o, tokensΓëê36+14
2025-05-23 08:07:18.659 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is **hexafluoroethane-based polymer**.
2025-05-23 08:07:18.662 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is **hexafluoroethane-based polymer**.'
2025-05-23 08:07:18.663 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane Polymer XYZ-2025', constituent: 'The primary material is **hexafluoroethane-based polymer**.'
2025-05-23 08:07:18.664 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is **hexafluoroethane-based polymer**. Hexafluoroethane Polymer XYZ-2025'
2025-05-23 08:07:18.665 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane Polymer XYZ-2025'
2025-05-23 08:07:18.666 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-23 08:07:19.741 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1074.75ms, model=gpt-4o, tokensΓëê205+0
2025-05-23 08:07:19.742 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-23 08:07:19.745 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-23 08:07:19.746 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-23 08:07:20.728 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 981.93ms, model=gpt-4o, tokensΓëê205+0
2025-05-23 08:07:20.729 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-23 08:07:20.732 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-23 08:07:20.734 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-23 08:07:20.735 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-23 08:07:20.736 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-23 08:07:20.736 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is **hexafluoroethane-based polymer**. Hexafluoroethane Polymer XYZ-2025'
2025-05-23 08:07:25.990 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 5252.90ms, returned 125 results
2025-05-23 08:07:25.992 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexamethylenediamine production', 'polystyrene production, extruded, HFC-152a blown', 'cyclohexane production, benzene hydrogenation, liquid phase process']
2025-05-23 08:07:25.994 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.21887074410915375, 0.21887074410915375, 0.24469581246376038, 0.24876102805137634, 0.25837114453315735]
2025-05-23 08:07:25.995 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-23 08:07:25.996 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is **hexafluoroethane-based polymer**. Hexafluoroethane Polymer XYZ-2025'
2025-05-23 08:07:26.045 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-23 08:07:32.429 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 6384.78ms, model=gpt-4o, tokensΓëê23942+99
2025-05-23 08:07:32.430 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-23 08:07:32.683 | INFO     | emissions_factor_matching.predictions:get_closest_match:308 - ajith_aravind Selected match: 74731bed-b705-59e2-adf2-cb634df573cd with confidence: high
2025-05-23 08:07:32.685 | INFO     | emissions_factor_matching.predictions:get_closest_match:309 - ajith_aravind Match explanation: The activity 'polyvinylfluoride production' is the closest match to the hexafluoroethane-based polymer. Both involve halogenated polymers, and the reference product 'polyvinylfluoride' aligns with the chemical structure and application of hexafluoroethane-based polymers.
2025-05-23 08:07:32.687 | INFO     | emissions_factor_matching.predictions:get_closest_match:310 - ajith_aravind Re-ranking completed in 6684.73ms
2025-05-23 08:07:32.688 | INFO     | emissions_factor_matching.api:get_recommended_activities:331 - ajith_aravind Selected match: 74731bed-b705-59e2-adf2-cb634df573cd with confidence: high
2025-05-23 08:07:32.689 | INFO     | emissions_factor_matching.api:get_recommended_activities:332 - ajith_aravind Match explanation: The activity 'polyvinylfluoride production' is the closest match to the hexafluoroethane-based polymer. Both involve halogenated polymers, and the reference product 'polyvinylfluoride' aligns with the chemical structure and application of hexafluoroethane-based polymers.
2025-05-23 08:07:32.689 | INFO     | emissions_factor_matching.api:get_recommended_activities:338 - ajith_aravind Finding geography match for selected activity, target geography: GLO
2025-05-23 08:07:32.690 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyvinylfluoride production', target geography: GLO
2025-05-23 08:07:32.691 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyvinylfluoride production', target geography: GLO
2025-05-23 08:07:32.692 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:32.692 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:32.732 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:32.747 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyvinylfluoride production'
2025-05-23 08:07:32.748 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 56.86ms
2025-05-23 08:07:32.749 | INFO     | emissions_factor_matching.api:get_recommended_activities:357 - ajith_aravind Resolved geography: RoW for activity: 'polyvinylfluoride production'
2025-05-23 08:07:32.750 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'hexafluoroethane production, from fluorination of tetrafluoroethane', target geography: GLO
2025-05-23 08:07:32.751 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'hexafluoroethane production, from fluorination of tetrafluoroethane', target geography: GLO
2025-05-23 08:07:32.751 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:32.752 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:32.766 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'hexafluoroethane production, from fluorination of tetrafluoroethane'
2025-05-23 08:07:32.767 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 16.18ms
2025-05-23 08:07:32.768 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'hexafluoroethane production, from fluorination of tetrafluoroethane', target geography: GLO
2025-05-23 08:07:32.769 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'hexafluoroethane production, from fluorination of tetrafluoroethane', target geography: GLO
2025-05-23 08:07:32.770 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:32.771 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:32.784 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'hexafluoroethane production, from fluorination of tetrafluoroethane'
2025-05-23 08:07:32.785 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 15.73ms
2025-05-23 08:07:32.786 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'hexamethylenediamine production', target geography: GLO
2025-05-23 08:07:32.787 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'hexamethylenediamine production', target geography: GLO
2025-05-23 08:07:32.788 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:32.789 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:32.804 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:32.819 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'hexamethylenediamine production'
2025-05-23 08:07:32.820 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 33.48ms
2025-05-23 08:07:32.821 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polystyrene production, extruded, HFC-152a blown', target geography: GLO
2025-05-23 08:07:32.822 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polystyrene production, extruded, HFC-152a blown', target geography: GLO
2025-05-23 08:07:32.823 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:32.824 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:32.837 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:32.851 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polystyrene production, extruded, HFC-152a blown'
2025-05-23 08:07:32.853 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.19ms
2025-05-23 08:07:32.854 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'cyclohexane production, benzene hydrogenation, liquid phase process', target geography: GLO
2025-05-23 08:07:32.854 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'cyclohexane production, benzene hydrogenation, liquid phase process', target geography: GLO
2025-05-23 08:07:32.855 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:32.856 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:32.869 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:32.883 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'cyclohexane production, benzene hydrogenation, liquid phase process'
2025-05-23 08:07:32.884 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.59ms
2025-05-23 08:07:32.885 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyvinylfluoride production, dispersion', target geography: GLO
2025-05-23 08:07:32.886 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyvinylfluoride production, dispersion', target geography: GLO
2025-05-23 08:07:32.887 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:32.887 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:32.902 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:32.918 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyvinylfluoride production, dispersion'
2025-05-23 08:07:32.919 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 33.30ms
2025-05-23 08:07:32.921 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'lithium hexafluorophosphate production', target geography: GLO
2025-05-23 08:07:32.923 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'lithium hexafluorophosphate production', target geography: GLO
2025-05-23 08:07:32.924 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:32.924 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:32.937 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:32.963 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'lithium hexafluorophosphate production'
2025-05-23 08:07:32.964 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 41.55ms
2025-05-23 08:07:32.965 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyvinyl chloride production, unspecified polymerisation, weighted average', target geography: GLO
2025-05-23 08:07:32.966 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyvinyl chloride production, unspecified polymerisation, weighted average', target geography: GLO
2025-05-23 08:07:32.967 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:32.968 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:32.983 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:32.999 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyvinyl chloride production, unspecified polymerisation, weighted average'
2025-05-23 08:07:33.000 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 33.69ms
2025-05-23 08:07:33.001 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'uranium hexafluoride production', target geography: GLO
2025-05-23 08:07:33.002 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'uranium hexafluoride production', target geography: GLO
2025-05-23 08:07:33.003 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.003 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.017 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.030 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'uranium hexafluoride production'
2025-05-23 08:07:33.032 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.80ms
2025-05-23 08:07:33.032 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyvinyl chloride production, emulsion polymerisation', target geography: GLO
2025-05-23 08:07:33.033 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyvinyl chloride production, emulsion polymerisation', target geography: GLO
2025-05-23 08:07:33.034 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.034 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.047 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.061 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyvinyl chloride production, emulsion polymerisation'
2025-05-23 08:07:33.062 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.98ms
2025-05-23 08:07:33.063 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'cyclohexanol production', target geography: GLO
2025-05-23 08:07:33.063 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'cyclohexanol production', target geography: GLO
2025-05-23 08:07:33.064 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.065 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.079 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.092 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'cyclohexanol production'
2025-05-23 08:07:33.093 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.03ms
2025-05-23 08:07:33.094 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polydimethylsiloxane production', target geography: GLO
2025-05-23 08:07:33.095 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polydimethylsiloxane production', target geography: GLO
2025-05-23 08:07:33.096 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.096 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.112 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.125 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polydimethylsiloxane production'
2025-05-23 08:07:33.126 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.79ms
2025-05-23 08:07:33.127 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'acrylonitrile-butadiene-styrene copolymer production', target geography: GLO
2025-05-23 08:07:33.128 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'acrylonitrile-butadiene-styrene copolymer production', target geography: GLO
2025-05-23 08:07:33.130 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.130 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.145 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.159 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'acrylonitrile-butadiene-styrene copolymer production'
2025-05-23 08:07:33.160 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 32.35ms
2025-05-23 08:07:33.162 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyether polyols production, long chain', target geography: GLO
2025-05-23 08:07:33.163 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyether polyols production, long chain', target geography: GLO
2025-05-23 08:07:33.164 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.165 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.177 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.191 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyether polyols production, long chain'
2025-05-23 08:07:33.192 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.23ms
2025-05-23 08:07:33.193 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'dimethyl hexanediol production', target geography: GLO
2025-05-23 08:07:33.194 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'dimethyl hexanediol production', target geography: GLO
2025-05-23 08:07:33.195 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.196 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.210 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'dimethyl hexanediol production'
2025-05-23 08:07:33.211 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 17.18ms
2025-05-23 08:07:33.213 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'cyclohexanone production', target geography: GLO
2025-05-23 08:07:33.213 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'cyclohexanone production', target geography: GLO
2025-05-23 08:07:33.214 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.215 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.228 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.245 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'cyclohexanone production'
2025-05-23 08:07:33.252 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 39.05ms
2025-05-23 08:07:33.254 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'sodium hexafluorophosphate production', target geography: GLO
2025-05-23 08:07:33.255 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'sodium hexafluorophosphate production', target geography: GLO
2025-05-23 08:07:33.255 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.256 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.270 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.285 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'sodium hexafluorophosphate production'
2025-05-23 08:07:33.287 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 31.87ms
2025-05-23 08:07:33.288 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyether polyols production, short chain', target geography: GLO
2025-05-23 08:07:33.289 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyether polyols production, short chain', target geography: GLO
2025-05-23 08:07:33.289 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.290 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.303 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.318 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyether polyols production, short chain'
2025-05-23 08:07:33.319 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.55ms
2025-05-23 08:07:33.320 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyvinyl chloride production, suspension polymerisation', target geography: GLO
2025-05-23 08:07:33.321 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyvinyl chloride production, suspension polymerisation', target geography: GLO
2025-05-23 08:07:33.322 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.322 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.335 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.349 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyvinyl chloride production, suspension polymerisation'
2025-05-23 08:07:33.350 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.04ms
2025-05-23 08:07:33.351 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'isohexane production', target geography: GLO
2025-05-23 08:07:33.352 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'isohexane production', target geography: GLO
2025-05-23 08:07:33.353 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.353 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.366 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.380 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'isohexane production'
2025-05-23 08:07:33.381 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.30ms
2025-05-23 08:07:33.383 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'ethylene vinyl acetate copolymer production', target geography: GLO
2025-05-23 08:07:33.383 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'ethylene vinyl acetate copolymer production', target geography: GLO
2025-05-23 08:07:33.384 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.384 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.397 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.412 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'ethylene vinyl acetate copolymer production'
2025-05-23 08:07:33.413 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.49ms
2025-05-23 08:07:33.414 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyurethane production, flexible foam, TDI-based, high density', target geography: GLO
2025-05-23 08:07:33.415 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyurethane production, flexible foam, TDI-based, high density', target geography: GLO
2025-05-23 08:07:33.416 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.417 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.430 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.444 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyurethane production, flexible foam, TDI-based, high density'
2025-05-23 08:07:33.445 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.86ms
2025-05-23 08:07:33.447 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for '1,1-difluoroethane production', target geography: GLO
2025-05-23 08:07:33.448 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for '1,1-difluoroethane production', target geography: GLO
2025-05-23 08:07:33.448 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.449 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.462 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.475 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: '1,1-difluoroethane production'
2025-05-23 08:07:33.477 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.93ms
2025-05-23 08:07:33.478 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polychloroprene production', target geography: GLO
2025-05-23 08:07:33.478 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polychloroprene production', target geography: GLO
2025-05-23 08:07:33.479 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.479 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.493 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.511 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polychloroprene production, falling back to first available
2025-05-23 08:07:33.512 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:33.514 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind DE
2025-05-23 08:07:33.515 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind US-LA
2025-05-23 08:07:33.516 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind JP
2025-05-23 08:07:33.517 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind CN
2025-05-23 08:07:33.517 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: DE for activity: 'polychloroprene production' (fallback)
2025-05-23 08:07:33.518 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: DE in 40.02ms
2025-05-23 08:07:33.519 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'methylcyclohexane production, from toluene hydrogenation', target geography: GLO
2025-05-23 08:07:33.520 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'methylcyclohexane production, from toluene hydrogenation', target geography: GLO
2025-05-23 08:07:33.521 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.521 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.538 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.552 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'methylcyclohexane production, from toluene hydrogenation'
2025-05-23 08:07:33.554 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 33.91ms
2025-05-23 08:07:33.555 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polycarbonate production', target geography: GLO
2025-05-23 08:07:33.556 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polycarbonate production', target geography: GLO
2025-05-23 08:07:33.557 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.557 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.570 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.584 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polycarbonate production'
2025-05-23 08:07:33.586 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.63ms
2025-05-23 08:07:33.587 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'extrusion, plastic film', target geography: GLO
2025-05-23 08:07:33.588 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'extrusion, plastic film', target geography: GLO
2025-05-23 08:07:33.589 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.589 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.606 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.622 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'extrusion, plastic film'
2025-05-23 08:07:33.623 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 34.92ms
2025-05-23 08:07:33.624 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyurethane production, flexible foam, TDI-based, flame retardant', target geography: GLO
2025-05-23 08:07:33.625 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyurethane production, flexible foam, TDI-based, flame retardant', target geography: GLO
2025-05-23 08:07:33.625 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.626 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.640 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.658 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyurethane production, flexible foam, TDI-based, flame retardant'
2025-05-23 08:07:33.660 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 34.78ms
2025-05-23 08:07:33.661 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'hexamethylene-1,6-diisocyanate production', target geography: GLO
2025-05-23 08:07:33.661 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'hexamethylene-1,6-diisocyanate production', target geography: GLO
2025-05-23 08:07:33.662 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.662 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.679 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.694 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'hexamethylene-1,6-diisocyanate production'
2025-05-23 08:07:33.696 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 34.41ms
2025-05-23 08:07:33.697 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'hexamethylene-1,6-diisocyanate production', target geography: GLO
2025-05-23 08:07:33.698 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'hexamethylene-1,6-diisocyanate production', target geography: GLO
2025-05-23 08:07:33.698 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.699 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.713 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.728 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'hexamethylene-1,6-diisocyanate production'
2025-05-23 08:07:33.729 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 31.66ms
2025-05-23 08:07:33.730 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polystyrene production, general purpose', target geography: GLO
2025-05-23 08:07:33.731 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polystyrene production, general purpose', target geography: GLO
2025-05-23 08:07:33.732 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.732 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.746 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.760 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polystyrene production, general purpose'
2025-05-23 08:07:33.761 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.97ms
2025-05-23 08:07:33.762 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyether polyols production, long chain, ISOPA', target geography: GLO
2025-05-23 08:07:33.763 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyether polyols production, long chain, ISOPA', target geography: GLO
2025-05-23 08:07:33.764 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.764 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.777 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.796 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyether polyols production, long chain, ISOPA, falling back to first available
2025-05-23 08:07:33.796 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:33.797 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:33.798 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyether polyols production, long chain, ISOPA' (fallback)
2025-05-23 08:07:33.799 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 36.07ms
2025-05-23 08:07:33.800 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'ethylvinylacetate production, foil', target geography: GLO
2025-05-23 08:07:33.801 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'ethylvinylacetate production, foil', target geography: GLO
2025-05-23 08:07:33.802 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.802 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.816 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.829 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'ethylvinylacetate production, foil'
2025-05-23 08:07:33.831 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.47ms
2025-05-23 08:07:33.831 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'dimethyl hexynediol production', target geography: GLO
2025-05-23 08:07:33.832 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'dimethyl hexynediol production', target geography: GLO
2025-05-23 08:07:33.833 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.833 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.846 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'dimethyl hexynediol production'
2025-05-23 08:07:33.847 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 15.04ms
2025-05-23 08:07:33.848 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'sulfur hexafluoride production, liquid', target geography: GLO
2025-05-23 08:07:33.849 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'sulfur hexafluoride production, liquid', target geography: GLO
2025-05-23 08:07:33.849 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.850 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.864 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.877 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'sulfur hexafluoride production, liquid'
2025-05-23 08:07:33.878 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.53ms
2025-05-23 08:07:33.879 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polystyrene production, extruded, CO2 blown', target geography: GLO
2025-05-23 08:07:33.880 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polystyrene production, extruded, CO2 blown', target geography: GLO
2025-05-23 08:07:33.881 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.881 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.897 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:33.916 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polystyrene production, extruded, CO2 blown'
2025-05-23 08:07:33.918 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 37.77ms
2025-05-23 08:07:33.919 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'monochloropentafluoroethane production, chlorofluorination of ethylene', target geography: GLO
2025-05-23 08:07:33.920 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'monochloropentafluoroethane production, chlorofluorination of ethylene', target geography: GLO
2025-05-23 08:07:33.921 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.922 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.940 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'monochloropentafluoroethane production, chlorofluorination of ethylene'
2025-05-23 08:07:33.943 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 22.25ms
2025-05-23 08:07:33.944 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'monochloropentafluoroethane production, chlorofluorination of ethylene', target geography: GLO
2025-05-23 08:07:33.945 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'monochloropentafluoroethane production, chlorofluorination of ethylene', target geography: GLO
2025-05-23 08:07:33.947 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.948 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.969 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'monochloropentafluoroethane production, chlorofluorination of ethylene'
2025-05-23 08:07:33.970 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 24.96ms
2025-05-23 08:07:33.971 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyurethane production, flexible foam, MDI-based', target geography: GLO
2025-05-23 08:07:33.972 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyurethane production, flexible foam, MDI-based', target geography: GLO
2025-05-23 08:07:33.974 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:33.975 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:33.992 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.006 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyurethane production, flexible foam, MDI-based'
2025-05-23 08:07:34.007 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 34.62ms
2025-05-23 08:07:34.007 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyvinylchloride, flexible, pellets, recycled to generic market for polyvinylchloride', target geography: GLO
2025-05-23 08:07:34.008 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyvinylchloride, flexible, pellets, recycled to generic market for polyvinylchloride', target geography: GLO
2025-05-23 08:07:34.008 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.009 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.021 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.039 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyvinylchloride, flexible, pellets, recycled to generic market for polyvinylchloride, falling back to first available
2025-05-23 08:07:34.040 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:34.041 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:34.042 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyvinylchloride, flexible, pellets, recycled to generic market for polyvinylchloride' (fallback)
2025-05-23 08:07:34.043 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 35.03ms
2025-05-23 08:07:34.044 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'chlorodifluoromethane production', target geography: GLO
2025-05-23 08:07:34.045 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'chlorodifluoromethane production', target geography: GLO
2025-05-23 08:07:34.045 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.046 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.059 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.078 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'chlorodifluoromethane production'
2025-05-23 08:07:34.079 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 34.22ms
2025-05-23 08:07:34.080 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polystyrene production, extruded, HFC-134a blown', target geography: GLO
2025-05-23 08:07:34.081 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polystyrene production, extruded, HFC-134a blown', target geography: GLO
2025-05-23 08:07:34.081 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.082 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.098 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.112 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polystyrene production, extruded, HFC-134a blown'
2025-05-23 08:07:34.114 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 32.99ms
2025-05-23 08:07:34.114 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'Polystyrene injection moulded hard plastic', target geography: GLO
2025-05-23 08:07:34.116 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'Polystyrene injection moulded hard plastic', target geography: GLO
2025-05-23 08:07:34.116 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.118 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.133 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.147 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'Polystyrene injection moulded hard plastic'
2025-05-23 08:07:34.148 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 31.94ms
2025-05-23 08:07:34.149 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polymer foaming', target geography: GLO
2025-05-23 08:07:34.150 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polymer foaming', target geography: GLO
2025-05-23 08:07:34.151 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.152 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.166 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.180 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polymer foaming'
2025-05-23 08:07:34.182 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 31.59ms
2025-05-23 08:07:34.183 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyurethane production, flexible foam, TDI-based, low density', target geography: GLO
2025-05-23 08:07:34.184 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyurethane production, flexible foam, TDI-based, low density', target geography: GLO
2025-05-23 08:07:34.185 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.186 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.199 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.213 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyurethane production, flexible foam, TDI-based, low density'
2025-05-23 08:07:34.214 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.92ms
2025-05-23 08:07:34.216 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polystyrene production, high impact', target geography: GLO
2025-05-23 08:07:34.217 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polystyrene production, high impact', target geography: GLO
2025-05-23 08:07:34.218 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.219 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.233 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.247 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polystyrene production, high impact'
2025-05-23 08:07:34.248 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.98ms
2025-05-23 08:07:34.249 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyvinylchloride, rigid, pellets, recycled to generic market for polyvinylchloride', target geography: GLO
2025-05-23 08:07:34.250 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyvinylchloride, rigid, pellets, recycled to generic market for polyvinylchloride', target geography: GLO
2025-05-23 08:07:34.251 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.252 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.265 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.283 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyvinylchloride, rigid, pellets, recycled to generic market for polyvinylchloride, falling back to first available
2025-05-23 08:07:34.284 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:34.285 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:34.286 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyvinylchloride, rigid, pellets, recycled to generic market for polyvinylchloride' (fallback)
2025-05-23 08:07:34.287 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 37.23ms
2025-05-23 08:07:34.288 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'perfluoropentane production', target geography: GLO
2025-05-23 08:07:34.289 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'perfluoropentane production', target geography: GLO
2025-05-23 08:07:34.290 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.290 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.304 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'perfluoropentane production'
2025-05-23 08:07:34.306 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 16.97ms
2025-05-23 08:07:34.308 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polystyrene production, expandable', target geography: GLO
2025-05-23 08:07:34.309 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polystyrene production, expandable', target geography: GLO
2025-05-23 08:07:34.310 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.310 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.323 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.337 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polystyrene production, expandable'
2025-05-23 08:07:34.338 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.69ms
2025-05-23 08:07:34.340 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'butanols production, hydroformylation of propylene', target geography: GLO
2025-05-23 08:07:34.341 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'butanols production, hydroformylation of propylene', target geography: GLO
2025-05-23 08:07:34.341 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.342 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.356 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.368 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'butanols production, hydroformylation of propylene'
2025-05-23 08:07:34.370 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.60ms
2025-05-23 08:07:34.371 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'butanols production, hydroformylation of propylene', target geography: GLO
2025-05-23 08:07:34.372 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'butanols production, hydroformylation of propylene', target geography: GLO
2025-05-23 08:07:34.373 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.374 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.387 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.403 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'butanols production, hydroformylation of propylene'
2025-05-23 08:07:34.404 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 32.05ms
2025-05-23 08:07:34.406 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'styrene-acrylonitrile copolymer production', target geography: GLO
2025-05-23 08:07:34.407 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'styrene-acrylonitrile copolymer production', target geography: GLO
2025-05-23 08:07:34.409 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.409 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.424 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.437 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'styrene-acrylonitrile copolymer production'
2025-05-23 08:07:34.438 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 31.10ms
2025-05-23 08:07:34.440 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for '2,5-dimethylhexane-2,5-dihydroperoxide production', target geography: GLO
2025-05-23 08:07:34.441 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for '2,5-dimethylhexane-2,5-dihydroperoxide production', target geography: GLO
2025-05-23 08:07:34.442 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.443 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.456 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: '2,5-dimethylhexane-2,5-dihydroperoxide production'
2025-05-23 08:07:34.458 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 16.20ms
2025-05-23 08:07:34.459 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'Polypropylene injection moulded hard plastic', target geography: GLO
2025-05-23 08:07:34.460 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'Polypropylene injection moulded hard plastic', target geography: GLO
2025-05-23 08:07:34.461 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.462 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.477 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.490 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'Polypropylene injection moulded hard plastic'
2025-05-23 08:07:34.491 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.52ms
2025-05-23 08:07:34.492 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'chloroprene production, from acetylene hydrochlorination', target geography: GLO
2025-05-23 08:07:34.493 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'chloroprene production, from acetylene hydrochlorination', target geography: GLO
2025-05-23 08:07:34.494 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.494 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.507 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.526 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: chloroprene production, from acetylene hydrochlorination, falling back to first available
2025-05-23 08:07:34.528 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:34.529 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind CN
2025-05-23 08:07:34.531 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind JP
2025-05-23 08:07:34.532 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: CN for activity: 'chloroprene production, from acetylene hydrochlorination' (fallback)
2025-05-23 08:07:34.533 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: CN in 40.47ms
2025-05-23 08:07:34.534 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyurethane production, rigid foam', target geography: GLO
2025-05-23 08:07:34.535 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyurethane production, rigid foam', target geography: GLO
2025-05-23 08:07:34.536 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.537 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.550 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.563 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyurethane production, rigid foam'
2025-05-23 08:07:34.564 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.69ms
2025-05-23 08:07:34.565 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyvinylfluoride, film production', target geography: GLO
2025-05-23 08:07:34.566 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyvinylfluoride, film production', target geography: GLO
2025-05-23 08:07:34.567 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.567 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.581 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.595 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyvinylfluoride, film production'
2025-05-23 08:07:34.596 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.62ms
2025-05-23 08:07:34.597 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'extrusion, co-extrusion of plastic sheets', target geography: GLO
2025-05-23 08:07:34.598 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'extrusion, co-extrusion of plastic sheets', target geography: GLO
2025-05-23 08:07:34.599 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.600 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.613 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.626 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'extrusion, co-extrusion of plastic sheets'
2025-05-23 08:07:34.628 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.57ms
2025-05-23 08:07:34.629 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'Recycled Polypropylene injection moulded hard plastic', target geography: GLO
2025-05-23 08:07:34.630 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'Recycled Polypropylene injection moulded hard plastic', target geography: GLO
2025-05-23 08:07:34.631 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.632 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.645 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.658 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'Recycled Polypropylene injection moulded hard plastic'
2025-05-23 08:07:34.660 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.58ms
2025-05-23 08:07:34.661 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyether polyols production, short chain, ISOPA', target geography: GLO
2025-05-23 08:07:34.662 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyether polyols production, short chain, ISOPA', target geography: GLO
2025-05-23 08:07:34.663 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.664 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.677 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.698 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyether polyols production, short chain, ISOPA, falling back to first available
2025-05-23 08:07:34.700 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:34.701 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:34.702 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyether polyols production, short chain, ISOPA' (fallback)
2025-05-23 08:07:34.703 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 41.12ms
2025-05-23 08:07:34.705 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polycarboxylates production, 40% active substance', target geography: GLO
2025-05-23 08:07:34.706 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polycarboxylates production, 40% active substance', target geography: GLO
2025-05-23 08:07:34.707 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.708 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.721 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.735 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polycarboxylates production, 40% active substance'
2025-05-23 08:07:34.737 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.85ms
2025-05-23 08:07:34.738 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'extrusion of plastic sheets and thermoforming, inline', target geography: GLO
2025-05-23 08:07:34.740 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'extrusion of plastic sheets and thermoforming, inline', target geography: GLO
2025-05-23 08:07:34.741 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.741 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.754 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.768 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'extrusion of plastic sheets and thermoforming, inline'
2025-05-23 08:07:34.770 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.02ms
2025-05-23 08:07:34.772 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'carbon fibre reinforced plastic, injection moulded', target geography: GLO
2025-05-23 08:07:34.773 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'carbon fibre reinforced plastic, injection moulded', target geography: GLO
2025-05-23 08:07:34.774 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.774 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.788 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'carbon fibre reinforced plastic, injection moulded'
2025-05-23 08:07:34.790 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 17.36ms
2025-05-23 08:07:34.791 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'acrylonitrile-butadiene-styrene, pellets, recycled to generic market for acrylonitrile-butadiene-styrene copolymer', target geography: GLO
2025-05-23 08:07:34.792 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'acrylonitrile-butadiene-styrene, pellets, recycled to generic market for acrylonitrile-butadiene-styrene copolymer', target geography: GLO
2025-05-23 08:07:34.793 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.794 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.807 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.825 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: acrylonitrile-butadiene-styrene, pellets, recycled to generic market for acrylonitrile-butadiene-styrene copolymer, falling back to first available
2025-05-23 08:07:34.826 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:34.827 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:34.828 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'acrylonitrile-butadiene-styrene, pellets, recycled to generic market for acrylonitrile-butadiene-styrene copolymer' (fallback)
2025-05-23 08:07:34.828 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 36.22ms
2025-05-23 08:07:34.830 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'bitumen seal production, polymer EP4 flame retardant', target geography: GLO
2025-05-23 08:07:34.831 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'bitumen seal production, polymer EP4 flame retardant', target geography: GLO
2025-05-23 08:07:34.832 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.833 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.846 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.860 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'bitumen seal production, polymer EP4 flame retardant'
2025-05-23 08:07:34.861 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.33ms
2025-05-23 08:07:34.862 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'Polyethylene injection moulded hard plastic', target geography: GLO
2025-05-23 08:07:34.863 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'Polyethylene injection moulded hard plastic', target geography: GLO
2025-05-23 08:07:34.864 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.865 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.878 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.891 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'Polyethylene injection moulded hard plastic'
2025-05-23 08:07:34.893 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.40ms
2025-05-23 08:07:34.893 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'fleece production, polyethylene', target geography: GLO
2025-05-23 08:07:34.894 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'fleece production, polyethylene', target geography: GLO
2025-05-23 08:07:34.895 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.896 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.909 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:34.923 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'fleece production, polyethylene'
2025-05-23 08:07:34.924 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.01ms
2025-05-23 08:07:34.925 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyurethane adhesive production', target geography: GLO
2025-05-23 08:07:34.926 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyurethane adhesive production', target geography: GLO
2025-05-23 08:07:34.927 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.928 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.942 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'polyurethane adhesive production'
2025-05-23 08:07:34.943 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 16.72ms
2025-05-23 08:07:34.944 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyacrylamide production', target geography: GLO
2025-05-23 08:07:34.945 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyacrylamide production', target geography: GLO
2025-05-23 08:07:34.946 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.947 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.960 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'polyacrylamide production'
2025-05-23 08:07:34.961 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 15.79ms
2025-05-23 08:07:34.963 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'trifluoromethane production', target geography: GLO
2025-05-23 08:07:34.963 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'trifluoromethane production', target geography: GLO
2025-05-23 08:07:34.964 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.964 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.977 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'trifluoromethane production'
2025-05-23 08:07:34.978 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 15.10ms
2025-05-23 08:07:34.980 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyester resin production, unsaturated', target geography: GLO
2025-05-23 08:07:34.980 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyester resin production, unsaturated', target geography: GLO
2025-05-23 08:07:34.981 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:34.982 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:34.995 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.009 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyester resin production, unsaturated'
2025-05-23 08:07:35.010 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.37ms
2025-05-23 08:07:35.011 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'isophthalic acid based unsaturated polyester resin production', target geography: GLO
2025-05-23 08:07:35.012 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'isophthalic acid based unsaturated polyester resin production', target geography: GLO
2025-05-23 08:07:35.012 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.013 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.026 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.039 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'isophthalic acid based unsaturated polyester resin production'
2025-05-23 08:07:35.041 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.53ms
2025-05-23 08:07:35.042 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'chemical production, inorganic', target geography: GLO
2025-05-23 08:07:35.043 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'chemical production, inorganic', target geography: GLO
2025-05-23 08:07:35.044 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.044 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.057 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'chemical production, inorganic'
2025-05-23 08:07:35.058 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 15.52ms
2025-05-23 08:07:35.060 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polymethyl methacrylate production', target geography: GLO
2025-05-23 08:07:35.060 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polymethyl methacrylate production', target geography: GLO
2025-05-23 08:07:35.061 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.062 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.075 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.092 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polymethyl methacrylate production, falling back to first available
2025-05-23 08:07:35.093 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.094 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RAS
2025-05-23 08:07:35.095 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.096 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RNA
2025-05-23 08:07:35.097 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RAS for activity: 'polymethyl methacrylate production' (fallback)
2025-05-23 08:07:35.099 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RAS in 38.03ms
2025-05-23 08:07:35.099 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'hafnium sponge production, from hafnium tetrachloride', target geography: GLO
2025-05-23 08:07:35.100 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'hafnium sponge production, from hafnium tetrachloride', target geography: GLO
2025-05-23 08:07:35.101 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.102 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.115 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.128 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'hafnium sponge production, from hafnium tetrachloride'
2025-05-23 08:07:35.129 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.20ms
2025-05-23 08:07:35.130 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene, high density, pellets, recycled to generic market for polyethylene, high density, granulate', target geography: GLO
2025-05-23 08:07:35.132 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene, high density, pellets, recycled to generic market for polyethylene, high density, granulate', target geography: GLO
2025-05-23 08:07:35.133 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.133 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.146 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.164 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyethylene, high density, pellets, recycled to generic market for polyethylene, high density, granulate, falling back to first available
2025-05-23 08:07:35.165 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.166 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.167 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyethylene, high density, pellets, recycled to generic market for polyethylene, high density, granulate' (fallback)
2025-05-23 08:07:35.169 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 36.99ms
2025-05-23 08:07:35.170 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polystyrene, pellets, recycled to generic market for polystyrene, high impact', target geography: GLO
2025-05-23 08:07:35.170 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polystyrene, pellets, recycled to generic market for polystyrene, high impact', target geography: GLO
2025-05-23 08:07:35.171 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.172 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.185 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.204 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polystyrene, pellets, recycled to generic market for polystyrene, high impact, falling back to first available
2025-05-23 08:07:35.205 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.206 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.207 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polystyrene, pellets, recycled to generic market for polystyrene, high impact' (fallback)
2025-05-23 08:07:35.208 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 37.62ms
2025-05-23 08:07:35.209 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'orthophthalic acid based unsaturated polyester resin production', target geography: GLO
2025-05-23 08:07:35.210 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'orthophthalic acid based unsaturated polyester resin production', target geography: GLO
2025-05-23 08:07:35.211 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.212 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.224 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.239 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'orthophthalic acid based unsaturated polyester resin production'
2025-05-23 08:07:35.241 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.52ms
2025-05-23 08:07:35.242 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'textile production, nonwoven polypropylene, spunbond', target geography: GLO
2025-05-23 08:07:35.243 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'textile production, nonwoven polypropylene, spunbond', target geography: GLO
2025-05-23 08:07:35.244 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.245 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.258 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.271 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'textile production, nonwoven polypropylene, spunbond'
2025-05-23 08:07:35.273 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.69ms
2025-05-23 08:07:35.274 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene/polypropylene, pellets, recycled to generic markets for PP and HDPE', target geography: GLO
2025-05-23 08:07:35.276 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene/polypropylene, pellets, recycled to generic markets for PP and HDPE', target geography: GLO
2025-05-23 08:07:35.277 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.277 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.290 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.309 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyethylene/polypropylene, pellets, recycled to generic markets for PP and HDPE, falling back to first available
2025-05-23 08:07:35.310 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.311 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.312 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyethylene/polypropylene, pellets, recycled to generic markets for PP and HDPE' (fallback)
2025-05-23 08:07:35.313 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 37.23ms
2025-05-23 08:07:35.314 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene/polypropylene, pellets, recycled to generic markets for PP and HDPE', target geography: GLO
2025-05-23 08:07:35.315 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene/polypropylene, pellets, recycled to generic markets for PP and HDPE', target geography: GLO
2025-05-23 08:07:35.316 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.316 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.329 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.348 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyethylene/polypropylene, pellets, recycled to generic markets for PP and HDPE, falling back to first available
2025-05-23 08:07:35.349 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.350 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.351 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyethylene/polypropylene, pellets, recycled to generic markets for PP and HDPE' (fallback)
2025-05-23 08:07:35.352 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 37.59ms
2025-05-23 08:07:35.354 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'methyl methacrylate production, acetone cyanohydrin process, with sulfuric acid recovery', target geography: GLO
2025-05-23 08:07:35.355 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'methyl methacrylate production, acetone cyanohydrin process, with sulfuric acid recovery', target geography: GLO
2025-05-23 08:07:35.355 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.356 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.369 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.387 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: methyl methacrylate production, acetone cyanohydrin process, with sulfuric acid recovery, falling back to first available
2025-05-23 08:07:35.388 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.389 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RAS
2025-05-23 08:07:35.390 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RNA
2025-05-23 08:07:35.391 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.392 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RAS for activity: 'methyl methacrylate production, acetone cyanohydrin process, with sulfuric acid recovery' (fallback)
2025-05-23 08:07:35.393 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RAS in 38.53ms
2025-05-23 08:07:35.394 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'methyl methacrylate production, acetone cyanohydrin process, with sulfuric acid recovery', target geography: GLO
2025-05-23 08:07:35.395 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'methyl methacrylate production, acetone cyanohydrin process, with sulfuric acid recovery', target geography: GLO
2025-05-23 08:07:35.396 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.396 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.409 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.427 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: methyl methacrylate production, acetone cyanohydrin process, with sulfuric acid recovery, falling back to first available
2025-05-23 08:07:35.428 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.429 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RAS
2025-05-23 08:07:35.430 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RNA
2025-05-23 08:07:35.432 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.433 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RAS for activity: 'methyl methacrylate production, acetone cyanohydrin process, with sulfuric acid recovery' (fallback)
2025-05-23 08:07:35.434 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RAS in 38.82ms
2025-05-23 08:07:35.435 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'heptane to generic market for solvent, organic', target geography: GLO
2025-05-23 08:07:35.436 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'heptane to generic market for solvent, organic', target geography: GLO
2025-05-23 08:07:35.437 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.438 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.451 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'heptane to generic market for solvent, organic'
2025-05-23 08:07:35.452 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 16.72ms
2025-05-23 08:07:35.453 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene, pellets, recycled to generic markets for LDPE and HDPE', target geography: GLO
2025-05-23 08:07:35.454 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene, pellets, recycled to generic markets for LDPE and HDPE', target geography: GLO
2025-05-23 08:07:35.455 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.455 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.468 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.486 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyethylene, pellets, recycled to generic markets for LDPE and HDPE, falling back to first available
2025-05-23 08:07:35.488 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.489 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.490 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyethylene, pellets, recycled to generic markets for LDPE and HDPE' (fallback)
2025-05-23 08:07:35.492 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 37.79ms
2025-05-23 08:07:35.493 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene, pellets, recycled to generic markets for LDPE and HDPE', target geography: GLO
2025-05-23 08:07:35.493 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene, pellets, recycled to generic markets for LDPE and HDPE', target geography: GLO
2025-05-23 08:07:35.495 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.495 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.508 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.528 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyethylene, pellets, recycled to generic markets for LDPE and HDPE, falling back to first available
2025-05-23 08:07:35.529 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.530 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.531 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyethylene, pellets, recycled to generic markets for LDPE and HDPE' (fallback)
2025-05-23 08:07:35.532 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 38.27ms
2025-05-23 08:07:35.533 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'fluazifop-butyl production', target geography: GLO
2025-05-23 08:07:35.533 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'fluazifop-butyl production', target geography: GLO
2025-05-23 08:07:35.534 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.535 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.547 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'fluazifop-butyl production'
2025-05-23 08:07:35.549 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 15.24ms
2025-05-23 08:07:35.550 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene, low density, pellets, coloured, recycled to generic market for polyethylene, low density, granulate', target geography: GLO
2025-05-23 08:07:35.551 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene, low density, pellets, coloured, recycled to generic market for polyethylene, low density, granulate', target geography: GLO
2025-05-23 08:07:35.552 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.552 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.565 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.582 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyethylene, low density, pellets, coloured, recycled to generic market for polyethylene, low density, granulate, falling back to first available
2025-05-23 08:07:35.583 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.584 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.585 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyethylene, low density, pellets, coloured, recycled to generic market for polyethylene, low density, granulate' (fallback)
2025-05-23 08:07:35.586 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 35.44ms
2025-05-23 08:07:35.587 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'melamine urea formaldehyde adhesive production', target geography: GLO
2025-05-23 08:07:35.588 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'melamine urea formaldehyde adhesive production', target geography: GLO
2025-05-23 08:07:35.589 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.590 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.604 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'melamine urea formaldehyde adhesive production'
2025-05-23 08:07:35.606 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 17.48ms
2025-05-23 08:07:35.607 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'HDPE Plastic Sheets', target geography: GLO
2025-05-23 08:07:35.608 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'HDPE Plastic Sheets', target geography: GLO
2025-05-23 08:07:35.609 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.609 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.622 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.635 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'HDPE Plastic Sheets'
2025-05-23 08:07:35.637 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.93ms
2025-05-23 08:07:35.639 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyvinylchloride, micronised powder, recycled to generic market for polyvinylchloride', target geography: GLO
2025-05-23 08:07:35.640 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyvinylchloride, micronised powder, recycled to generic market for polyvinylchloride', target geography: GLO
2025-05-23 08:07:35.641 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.641 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.654 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.673 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyvinylchloride, micronised powder, recycled to generic market for polyvinylchloride, falling back to first available
2025-05-23 08:07:35.674 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.675 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.677 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyvinylchloride, micronised powder, recycled to generic market for polyvinylchloride' (fallback)
2025-05-23 08:07:35.678 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 38.43ms
2025-05-23 08:07:35.679 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'dicyclopentadiene based unsaturated polyester resin production', target geography: GLO
2025-05-23 08:07:35.680 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'dicyclopentadiene based unsaturated polyester resin production', target geography: GLO
2025-05-23 08:07:35.681 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.681 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.694 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.707 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'dicyclopentadiene based unsaturated polyester resin production'
2025-05-23 08:07:35.708 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.33ms
2025-05-23 08:07:35.709 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'urea formaldehyde resin production', target geography: GLO
2025-05-23 08:07:35.710 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'urea formaldehyde resin production', target geography: GLO
2025-05-23 08:07:35.711 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.712 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.725 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.739 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'urea formaldehyde resin production'
2025-05-23 08:07:35.740 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.97ms
2025-05-23 08:07:35.741 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyvinylidenchloride production, granulate', target geography: GLO
2025-05-23 08:07:35.742 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyvinylidenchloride production, granulate', target geography: GLO
2025-05-23 08:07:35.743 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.744 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.757 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.771 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyvinylidenchloride production, granulate'
2025-05-23 08:07:35.773 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 30.27ms
2025-05-23 08:07:35.774 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'propyl acetate production', target geography: GLO
2025-05-23 08:07:35.775 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'propyl acetate production', target geography: GLO
2025-05-23 08:07:35.776 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.776 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.789 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.804 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'propyl acetate production'
2025-05-23 08:07:35.805 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.81ms
2025-05-23 08:07:35.806 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'recycled polyethylene terephthalate unsaturated polyester resin production', target geography: GLO
2025-05-23 08:07:35.807 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'recycled polyethylene terephthalate unsaturated polyester resin production', target geography: GLO
2025-05-23 08:07:35.808 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.808 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.822 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.839 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: recycled polyethylene terephthalate unsaturated polyester resin production, falling back to first available
2025-05-23 08:07:35.840 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.841 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:35.843 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'recycled polyethylene terephthalate unsaturated polyester resin production' (fallback)
2025-05-23 08:07:35.844 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 37.51ms
2025-05-23 08:07:35.846 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'phenolic resin production', target geography: GLO
2025-05-23 08:07:35.847 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'phenolic resin production', target geography: GLO
2025-05-23 08:07:35.847 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.848 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.861 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.875 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'phenolic resin production'
2025-05-23 08:07:35.876 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.59ms
2025-05-23 08:07:35.877 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene terephthalate, pellets, recycled fibre based to generic market for PET, granulate, amorphous', target geography: GLO
2025-05-23 08:07:35.878 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene terephthalate, pellets, recycled fibre based to generic market for PET, granulate, amorphous', target geography: GLO
2025-05-23 08:07:35.879 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.879 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.892 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:35.919 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyethylene terephthalate, pellets, recycled fibre based to generic market for PET, granulate, amorphous, falling back to first available
2025-05-23 08:07:35.920 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:35.922 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind CN-ZJ
2025-05-23 08:07:35.924 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: CN-ZJ for activity: 'polyethylene terephthalate, pellets, recycled fibre based to generic market for PET, granulate, amorphous' (fallback)
2025-05-23 08:07:35.925 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: CN-ZJ in 47.29ms
2025-05-23 08:07:35.926 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'hexamethyldisilazane production, amination of chlorosilane', target geography: GLO
2025-05-23 08:07:35.927 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'hexamethyldisilazane production, amination of chlorosilane', target geography: GLO
2025-05-23 08:07:35.929 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.930 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.944 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'hexamethyldisilazane production, amination of chlorosilane'
2025-05-23 08:07:35.946 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 18.29ms
2025-05-23 08:07:35.947 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'hexamethyldisilazane production, amination of chlorosilane', target geography: GLO
2025-05-23 08:07:35.948 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'hexamethyldisilazane production, amination of chlorosilane', target geography: GLO
2025-05-23 08:07:35.949 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.949 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.965 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'hexamethyldisilazane production, amination of chlorosilane'
2025-05-23 08:07:35.967 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 18.42ms
2025-05-23 08:07:35.968 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'pultrusion, thermoset resins', target geography: GLO
2025-05-23 08:07:35.969 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'pultrusion, thermoset resins', target geography: GLO
2025-05-23 08:07:35.970 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:35.970 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:35.985 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.000 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'pultrusion, thermoset resins'
2025-05-23 08:07:36.001 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 31.98ms
2025-05-23 08:07:36.002 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'chemical production, organic', target geography: GLO
2025-05-23 08:07:36.003 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'chemical production, organic', target geography: GLO
2025-05-23 08:07:36.004 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.004 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.018 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'chemical production, organic'
2025-05-23 08:07:36.019 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 16.42ms
2025-05-23 08:07:36.021 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polypropylene, pellets, recycled to generic market for polypropylene, granulate', target geography: GLO
2025-05-23 08:07:36.022 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polypropylene, pellets, recycled to generic market for polypropylene, granulate', target geography: GLO
2025-05-23 08:07:36.022 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.023 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.035 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.054 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polypropylene, pellets, recycled to generic market for polypropylene, granulate, falling back to first available
2025-05-23 08:07:36.056 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:36.058 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:36.059 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polypropylene, pellets, recycled to generic market for polypropylene, granulate' (fallback)
2025-05-23 08:07:36.060 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 38.51ms
2025-05-23 08:07:36.061 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyaluminium chloride production', target geography: GLO
2025-05-23 08:07:36.062 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyaluminium chloride production', target geography: GLO
2025-05-23 08:07:36.063 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.064 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.076 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: GLO for activity: 'polyaluminium chloride production'
2025-05-23 08:07:36.078 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: GLO in 16.11ms
2025-05-23 08:07:36.079 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'pentanols production, hydroformylation of butene', target geography: GLO
2025-05-23 08:07:36.080 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'pentanols production, hydroformylation of butene', target geography: GLO
2025-05-23 08:07:36.081 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.081 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.094 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.107 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'pentanols production, hydroformylation of butene'
2025-05-23 08:07:36.109 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.71ms
2025-05-23 08:07:36.110 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'pentanols production, hydroformylation of butene', target geography: GLO
2025-05-23 08:07:36.111 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'pentanols production, hydroformylation of butene', target geography: GLO
2025-05-23 08:07:36.112 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.112 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.126 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.139 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'pentanols production, hydroformylation of butene'
2025-05-23 08:07:36.140 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.85ms
2025-05-23 08:07:36.141 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'pentanols production, hydroformylation of butene', target geography: GLO
2025-05-23 08:07:36.142 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'pentanols production, hydroformylation of butene', target geography: GLO
2025-05-23 08:07:36.143 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.144 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.157 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.170 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'pentanols production, hydroformylation of butene'
2025-05-23 08:07:36.171 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.37ms
2025-05-23 08:07:36.172 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'vinyl acetate production, from ethylene acetoxylation', target geography: GLO
2025-05-23 08:07:36.174 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'vinyl acetate production, from ethylene acetoxylation', target geography: GLO
2025-05-23 08:07:36.174 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.175 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.188 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.201 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'vinyl acetate production, from ethylene acetoxylation'
2025-05-23 08:07:36.202 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.78ms
2025-05-23 08:07:36.203 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene terephthalate, pellets, coloured, recycled to generic market for PET, granulate, amorphous', target geography: GLO
2025-05-23 08:07:36.204 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene terephthalate, pellets, coloured, recycled to generic market for PET, granulate, amorphous', target geography: GLO
2025-05-23 08:07:36.205 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.206 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.218 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.237 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyethylene terephthalate, pellets, coloured, recycled to generic market for PET, granulate, amorphous, falling back to first available
2025-05-23 08:07:36.238 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:36.239 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:36.240 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyethylene terephthalate, pellets, coloured, recycled to generic market for PET, granulate, amorphous' (fallback)
2025-05-23 08:07:36.241 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 36.90ms
2025-05-23 08:07:36.243 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene terephthalate, labels to generic market for PET, granulate, amorphous', target geography: GLO
2025-05-23 08:07:36.244 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene terephthalate, labels to generic market for PET, granulate, amorphous', target geography: GLO
2025-05-23 08:07:36.244 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.245 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.259 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.276 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyethylene terephthalate, labels to generic market for PET, granulate, amorphous, falling back to first available
2025-05-23 08:07:36.278 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:36.279 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:36.280 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyethylene terephthalate, labels to generic market for PET, granulate, amorphous' (fallback)
2025-05-23 08:07:36.281 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 37.07ms
2025-05-23 08:07:36.282 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'diphenylether-compound production', target geography: GLO
2025-05-23 08:07:36.282 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'diphenylether-compound production', target geography: GLO
2025-05-23 08:07:36.283 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.284 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.296 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.310 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'diphenylether-compound production'
2025-05-23 08:07:36.311 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.45ms
2025-05-23 08:07:36.312 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'glass fibre reinforced plastic production, polyamide, injection moulded', target geography: GLO
2025-05-23 08:07:36.312 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'glass fibre reinforced plastic production, polyamide, injection moulded', target geography: GLO
2025-05-23 08:07:36.313 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.314 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.327 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.341 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'glass fibre reinforced plastic production, polyamide, injection moulded'
2025-05-23 08:07:36.342 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.74ms
2025-05-23 08:07:36.343 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene production, high density, granulate', target geography: GLO
2025-05-23 08:07:36.344 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene production, high density, granulate', target geography: GLO
2025-05-23 08:07:36.345 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.346 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.359 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.377 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyethylene production, high density, granulate'
2025-05-23 08:07:36.379 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 35.21ms
2025-05-23 08:07:36.380 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene, low density, pellets, recycled to generic market for polyethylene, low density, granulate', target geography: GLO
2025-05-23 08:07:36.381 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene, low density, pellets, recycled to generic market for polyethylene, low density, granulate', target geography: GLO
2025-05-23 08:07:36.382 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.383 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.396 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.414 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: polyethylene, low density, pellets, recycled to generic market for polyethylene, low density, granulate, falling back to first available
2025-05-23 08:07:36.415 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:36.417 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:36.418 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'polyethylene, low density, pellets, recycled to generic market for polyethylene, low density, granulate' (fallback)
2025-05-23 08:07:36.420 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 38.21ms
2025-05-23 08:07:36.421 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'methyl methacrylate production, acetone cyanohydrin process, with ammonium sulfate recovery', target geography: GLO
2025-05-23 08:07:36.422 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'methyl methacrylate production, acetone cyanohydrin process, with ammonium sulfate recovery', target geography: GLO
2025-05-23 08:07:36.422 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.423 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.436 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.455 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: methyl methacrylate production, acetone cyanohydrin process, with ammonium sulfate recovery, falling back to first available
2025-05-23 08:07:36.456 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:36.457 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:36.458 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RAS
2025-05-23 08:07:36.460 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RNA
2025-05-23 08:07:36.461 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'methyl methacrylate production, acetone cyanohydrin process, with ammonium sulfate recovery' (fallback)
2025-05-23 08:07:36.462 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 40.24ms
2025-05-23 08:07:36.463 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'methyl methacrylate production, acetone cyanohydrin process, with ammonium sulfate recovery', target geography: GLO
2025-05-23 08:07:36.464 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'methyl methacrylate production, acetone cyanohydrin process, with ammonium sulfate recovery', target geography: GLO
2025-05-23 08:07:36.465 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.466 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.479 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.497 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: methyl methacrylate production, acetone cyanohydrin process, with ammonium sulfate recovery, falling back to first available
2025-05-23 08:07:36.498 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:36.499 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:36.500 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RAS
2025-05-23 08:07:36.501 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RNA
2025-05-23 08:07:36.502 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'methyl methacrylate production, acetone cyanohydrin process, with ammonium sulfate recovery' (fallback)
2025-05-23 08:07:36.504 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 40.14ms
2025-05-23 08:07:36.505 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'PS/ABS, pellets, recycled to generic market for polystyrene, high impact', target geography: GLO
2025-05-23 08:07:36.506 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'PS/ABS, pellets, recycled to generic market for polystyrene, high impact', target geography: GLO
2025-05-23 08:07:36.507 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.508 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.521 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.540 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - ajith_aravind No exact geography match for: PS/ABS, pellets, recycled to generic market for polystyrene, high impact, falling back to first available
2025-05-23 08:07:36.541 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:169 - ajith_aravind Available geographies in ecoinvent:
2025-05-23 08:07:36.542 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:174 - ajith_aravind RER
2025-05-23 08:07:36.543 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:176 - ajith_aravind Resolved geography: RER for activity: 'PS/ABS, pellets, recycled to generic market for polystyrene, high impact' (fallback)
2025-05-23 08:07:36.544 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RER in 38.08ms
2025-05-23 08:07:36.545 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'epoxy resin production, liquid', target geography: GLO
2025-05-23 08:07:36.546 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'epoxy resin production, liquid', target geography: GLO
2025-05-23 08:07:36.547 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.547 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.559 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.573 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'epoxy resin production, liquid'
2025-05-23 08:07:36.574 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.29ms
2025-05-23 08:07:36.575 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polyethylene, high density, granulate, recycled to generic market for polyethylene, high density, granulate', target geography: GLO
2025-05-23 08:07:36.576 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polyethylene, high density, granulate, recycled to generic market for polyethylene, high density, granulate', target geography: GLO
2025-05-23 08:07:36.577 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.578 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.591 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.605 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polyethylene, high density, granulate, recycled to generic market for polyethylene, high density, granulate'
2025-05-23 08:07:36.606 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.77ms
2025-05-23 08:07:36.607 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'bisphenol A epoxy based vinyl ester resin production', target geography: GLO
2025-05-23 08:07:36.608 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'bisphenol A epoxy based vinyl ester resin production', target geography: GLO
2025-05-23 08:07:36.609 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.609 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.622 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.635 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'bisphenol A epoxy based vinyl ester resin production'
2025-05-23 08:07:36.637 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.63ms
2025-05-23 08:07:36.638 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'glycidyl methacrylate production, from methacrylic acid epichlorohydrin', target geography: GLO
2025-05-23 08:07:36.639 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'glycidyl methacrylate production, from methacrylic acid epichlorohydrin', target geography: GLO
2025-05-23 08:07:36.640 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.641 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.654 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.668 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'glycidyl methacrylate production, from methacrylic acid epichlorohydrin'
2025-05-23 08:07:36.669 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 29.76ms
2025-05-23 08:07:36.670 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'tetrahydrofuran production', target geography: GLO
2025-05-23 08:07:36.671 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'tetrahydrofuran production', target geography: GLO
2025-05-23 08:07:36.672 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.672 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.685 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.699 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'tetrahydrofuran production'
2025-05-23 08:07:36.700 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.73ms
2025-05-23 08:07:36.701 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'styrene production, from ethyl benzene dehydrogenation', target geography: GLO
2025-05-23 08:07:36.703 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'styrene production, from ethyl benzene dehydrogenation', target geography: GLO
2025-05-23 08:07:36.703 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.704 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.716 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.730 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'styrene production, from ethyl benzene dehydrogenation'
2025-05-23 08:07:36.731 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.15ms
2025-05-23 08:07:36.732 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:92 - ajith_aravind Finding geography match for 'polypropylene production, granulate', target geography: GLO
2025-05-23 08:07:36.733 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:133 - ajith_aravind Finding geography match for 'polypropylene production, granulate', target geography: GLO
2025-05-23 08:07:36.734 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:137 - ajith_aravind Geography priority list: ['GLO', 'RoW']
2025-05-23 08:07:36.734 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^GLO|GLO-|-GLO$
2025-05-23 08:07:36.748 | DEBUG    | emissions_factor_matching.geography:get_geography_activity_match:144 - ajith_aravind Trying geography pattern: ^RoW|RoW-|-RoW$
2025-05-23 08:07:36.760 | INFO     | emissions_factor_matching.geography:get_geography_activity_match:159 - ajith_aravind Found geography match: RoW for activity: 'polypropylene production, granulate'
2025-05-23 08:07:36.762 | INFO     | emissions_factor_matching.api:get_activity_from_dataset:102 - ajith_aravind Geography match found: RoW in 28.86ms
2025-05-23 08:07:36.763 | INFO     | emissions_factor_matching.api:get_recommended_activities:373 - ajith_aravind Final matched activity: 'polyvinylfluoride production' (RoW)
2025-05-23 08:07:36.764 | INFO     | emissions_factor_matching.api:get_recommended_activities:374 - ajith_aravind Returning 124 alternative recommendations
2025-05-23 08:07:36.765 | INFO     | emissions_factor_matching.api:get_recommended_activities:375 - ajith_aravind Total processing time: 20966.72ms
