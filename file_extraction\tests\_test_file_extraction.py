import os
from unittest import TestCase
from fastapi.testclient import TestClient
from file_extraction.api import model_api


client = TestClient(model_api)

class TestFileExtraction(TestCase):
    def test_validation_files(self):
        current_dir = os.path.dirname(os.path.abspath(__file__))
        directory_path = os.path.join(current_dir, "validation_files")

        files = [
            os.path.join(directory_path, f)
            for f in os.listdir(directory_path)
            if os.path.isfile(os.path.join(directory_path, f))
        ]

        for file_path in files:
            with open(file_path, 'rb') as f:
                files_ = {'file': (os.path.basename(file_path), f, 'multipart/form-data')}
                response = client.post("/", files=files_)

                self.assertEqual(response.status_code, 200)
                data = response.json()

                self.assertEqual(len(data["product"]["nodes"]), 19)
                self.assertEqual(len(data["product"]["edges"]), 18)
