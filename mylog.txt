2025-05-28 11:34:20.543 | WARNING  | utils.cache:_get_all_keys:212 - _get_all_keys called on RedisCache - this operation can be expensive
2025-05-28 11:34:20.632 | INFO     | emissions_factor_matching.tests.test_api:setUp:23 - Cache clear response: 200
2025-05-28 11:34:20.642 | INFO     | emissions_factor_matching.tests.test_api:test_backward_compatibility_chemical_name:68 - {'detail': [{'type': 'missing', 'loc': ['body', 'chemicalName'], 'msg': 'Field required', 'input': {'chemical_name': 'steel', 'iso_code': 'US', 'product_category': 'construction'}, 'url': 'https://errors.pydantic.dev/2.6/v/missing'}]}
F2025-05-28 11:34:20.648 | WARNING  | utils.cache:_get_all_keys:212 - _get_all_keys called on RedisCache - this operation can be expensive
2025-05-28 11:34:20.651 | INFO     | emissions_factor_matching.tests.test_api:setUp:23 - Cache clear response: 200
2025-05-28 11:34:20.682 | INFO     | utils.cache:wrapper:347 - Cache miss for geography_match:{"args": [], "kwargs": {"activity_name": "market for electricity, medium voltage", "api_version": "latest", "reference_product_name": "electricity, medium voltage", "target_geography_iso": "US"}}, cached result with expiry 31536000s
2025-05-28 11:34:20.714 | INFO     | utils.cache:wrapper:347 - Cache miss for geography_match:{"args": [], "kwargs": {"activity_name": "market for sodium bicarbonate", "api_version": "latest", "reference_product_name": "sodium bicarbonate", "target_geography_iso": "GB"}}, cached result with expiry 31536000s
2025-05-28 11:34:20.763 | INFO     | utils.cache:wrapper:347 - Cache miss for geography_match:{"args": [], "kwargs": {"activity_name": "1,1-difluoroethane production", "api_version": "latest", "reference_product_name": "1,1-difluoroethane", "target_geography_iso": "GB"}}, cached result with expiry 31536000s
.2025-05-28 11:34:20.769 | WARNING  | utils.cache:_get_all_keys:212 - _get_all_keys called on RedisCache - this operation can be expensive
2025-05-28 11:34:20.774 | INFO     | emissions_factor_matching.tests.test_api:setUp:23 - Cache clear response: 200
2025-05-28 11:34:20.778 | INFO     | emissions_factor_matching.api:get_recommended_activities:204 - API Version: latest
2025-05-28 11:34:20.778 | INFO     | emissions_factor_matching.api:get_recommended_activities:205 - Phase 1: Enhanced Input Category Prediction - Starting
2025-05-28 11:34:22.859 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
2025-05-28 11:34:22.908 | INFO     | emissions_factor_matching.api:get_recommended_activities:249 - Phase 1 Complete: Enhanced category = CHEMICAL_POLYMER_PLASTIC
2025-05-28 11:34:22.911 | INFO     | emissions_factor_matching.api:get_recommended_activities:252 - Phase 2: Modifier Spotting - Starting
2025-05-28 11:34:25.587 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
2025-05-28 11:34:25.588 | INFO     | emissions_factor_matching.api:get_recommended_activities:254 - Phase 2 Complete: Extracted 2 modifiers = ['non-ionic', 'polyol']
2025-05-28 11:34:25.589 | INFO     | emissions_factor_matching.api:get_recommended_activities:257 - Phase 3: ISIC Classification Mapping - Starting
2025-05-28 11:34:28.432 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ISICClassificationResponse
2025-05-28 11:34:28.433 | INFO     | emissions_factor_matching.api:get_recommended_activities:259 - Phase 3 Complete: Mapped to 1 ISIC codes = ['2013']
2025-05-28 11:34:28.434 | INFO     | emissions_factor_matching.api:get_recommended_activities:262 - Phase 4: Query Text Augmentation - Starting
2025-05-28 11:34:30.870 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 11:34:30.871 | INFO     | emissions_factor_matching.predictions:augment_query_text:763 - Query augmentation successful: 'Production and processing of non-ionic polyol polymers within the chemical manufacturing sector. Includes synthesis of polyols through polymerization and chemical modification processes, focusing on applications in polyurethane production, coatings, and adhesives. Relevant to ISIC 2013, covering industrial polymer and plastic manufacturing activities.'
2025-05-28 11:34:30.872 | INFO     | emissions_factor_matching.api:get_recommended_activities:264 - Phase 4 Complete: Augmented query = 'Production and processing of non-ionic polyol polymers within the chemical manufacturing sector. Includes synthesis of polyols through polymerization and chemical modification processes, focusing on applications in polyurethane production, coatings, and adhesives. Relevant to ISIC 2013, covering industrial polymer and plastic manufacturing activities.'
2025-05-28 11:34:30.873 | INFO     | emissions_factor_matching.api:get_recommended_activities:267 - Phase 5: Dynamic Filter Construction - Starting
E2025-05-28 11:34:31.158 | WARNING  | utils.cache:_get_all_keys:212 - _get_all_keys called on RedisCache - this operation can be expensive
2025-05-28 11:34:31.164 | INFO     | emissions_factor_matching.tests.test_api:setUp:23 - Cache clear response: 200
2025-05-28 11:34:31.170 | INFO     | emissions_factor_matching.api:get_recommended_activities:204 - API Version: latest
2025-05-28 11:34:31.171 | INFO     | emissions_factor_matching.api:get_recommended_activities:205 - Phase 1: Enhanced Input Category Prediction - Starting
2025-05-28 11:34:35.030 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
2025-05-28 11:34:35.031 | INFO     | emissions_factor_matching.api:get_recommended_activities:249 - Phase 1 Complete: Enhanced category = CHEMICAL_POLYMER_PLASTIC
2025-05-28 11:34:35.032 | INFO     | emissions_factor_matching.api:get_recommended_activities:252 - Phase 2: Modifier Spotting - Starting
2025-05-28 11:34:37.586 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
2025-05-28 11:34:37.587 | INFO     | emissions_factor_matching.api:get_recommended_activities:254 - Phase 2 Complete: Extracted 2 modifiers = ['non-ionic', 'polyol']
2025-05-28 11:34:37.587 | INFO     | emissions_factor_matching.api:get_recommended_activities:257 - Phase 3: ISIC Classification Mapping - Starting
2025-05-28 11:34:39.528 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ISICClassificationResponse
2025-05-28 11:34:39.529 | INFO     | emissions_factor_matching.api:get_recommended_activities:259 - Phase 3 Complete: Mapped to 1 ISIC codes = ['2013']
2025-05-28 11:34:39.530 | INFO     | emissions_factor_matching.api:get_recommended_activities:262 - Phase 4: Query Text Augmentation - Starting
2025-05-28 11:34:42.338 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 11:34:42.339 | INFO     | emissions_factor_matching.predictions:augment_query_text:763 - Query augmentation successful: 'Production and processing of non-ionic polyols, a class of polymers used in the manufacture of plastics and resins. Industrial synthesis under ISIC 2013, involving chemical reactions such as polymerization and hydroxyl functionalization, typically for applications in coatings, adhesives, and flexible foams. Includes processes for achieving specific molecular weights and purity levels suitable for industrial-grade applications.'
2025-05-28 11:34:42.342 | INFO     | emissions_factor_matching.api:get_recommended_activities:264 - Phase 4 Complete: Augmented query = 'Production and processing of non-ionic polyols, a class of polymers used in the manufacture of plastics and resins. Industrial synthesis under ISIC 2013, involving chemical reactions such as polymerization and hydroxyl functionalization, typically for applications in coatings, adhesives, and flexible foams. Includes processes for achieving specific molecular weights and purity levels suitable for industrial-grade applications.'
2025-05-28 11:34:42.343 | INFO     | emissions_factor_matching.api:get_recommended_activities:267 - Phase 5: Dynamic Filter Construction - Starting
E2025-05-28 11:34:42.376 | WARNING  | utils.cache:_get_all_keys:212 - _get_all_keys called on RedisCache - this operation can be expensive
2025-05-28 11:34:42.379 | INFO     | emissions_factor_matching.tests.test_api:setUp:23 - Cache clear response: 200
2025-05-28 11:34:42.382 | INFO     | emissions_factor_matching.api:get_recommended_activities:204 - API Version: latest
2025-05-28 11:34:42.382 | INFO     | emissions_factor_matching.api:get_recommended_activities:205 - Phase 1: Enhanced Input Category Prediction - Starting
2025-05-28 11:34:44.329 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
2025-05-28 11:34:44.331 | INFO     | emissions_factor_matching.api:get_recommended_activities:249 - Phase 1 Complete: Enhanced category = PRODUCT_OTHER
2025-05-28 11:34:44.333 | INFO     | emissions_factor_matching.api:get_recommended_activities:252 - Phase 2: Modifier Spotting - Starting
2025-05-28 11:34:47.510 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
2025-05-28 11:34:47.511 | INFO     | emissions_factor_matching.api:get_recommended_activities:254 - Phase 2 Complete: Extracted 0 modifiers = []
2025-05-28 11:34:47.512 | INFO     | emissions_factor_matching.api:get_recommended_activities:257 - Phase 3: ISIC Classification Mapping - Starting
2025-05-28 11:34:49.992 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ISICClassificationResponse
2025-05-28 11:34:49.994 | INFO     | emissions_factor_matching.api:get_recommended_activities:259 - Phase 3 Complete: Mapped to 0 ISIC codes = []
2025-05-28 11:34:49.996 | INFO     | emissions_factor_matching.api:get_recommended_activities:262 - Phase 4: Query Text Augmentation - Starting
2025-05-28 11:34:53.711 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 11:34:53.712 | INFO     | emissions_factor_matching.predictions:augment_query_text:763 - Query augmentation successful: 'Adjuster components and related products used in mechanical systems and assemblies. Includes manufacturing processes for precision adjustment devices, materials used in production, and typical applications in machinery, automotive, and industrial equipment. Focus on lifecycle stages such as production, usage, and disposal, with attention to material properties and industry standards.'
2025-05-28 11:34:53.713 | INFO     | emissions_factor_matching.api:get_recommended_activities:264 - Phase 4 Complete: Augmented query = 'Adjuster components and related products used in mechanical systems and assemblies. Includes manufacturing processes for precision adjustment devices, materials used in production, and typical applications in machinery, automotive, and industrial equipment. Focus on lifecycle stages such as production, usage, and disposal, with attention to material properties and industry standards.'
2025-05-28 11:34:53.714 | INFO     | emissions_factor_matching.api:get_recommended_activities:267 - Phase 5: Dynamic Filter Construction - Starting
2025-05-28 11:34:53.714 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:851 - Adding section hint for PRODUCT: C - Manufacturing
2025-05-28 11:34:53.715 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:880 - Constructed dynamic filters with 2 conditions
2025-05-28 11:34:53.716 | DEBUG    | emissions_factor_matching.predictions:construct_dynamic_filters:881 - Filter details: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}]}
2025-05-28 11:34:53.716 | INFO     | emissions_factor_matching.api:get_recommended_activities:269 - Phase 5 Complete: Dynamic filters = {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}]}
2025-05-28 11:34:55.408 | INFO     | completions:get_chat_completion:208 - Completion: NONE
2025-05-28 11:34:56.471 | INFO     | emissions_factor_matching.api:get_recommended_activities:275 - CAS Number: NONE
2025-05-28 11:34:56.472 | INFO     | emissions_factor_matching.api:get_recommended_activities:278 - Phase 6: ChromaDB Vector Search Enhancement - Starting
2025-05-28 11:34:56.473 | INFO     | emissions_factor_matching.dataset:search_candidates:72 - phase_6_search Executing ChromaDB vector search
2025-05-28 11:34:56.475 | INFO     | emissions_factor_matching.dataset:search_candidates:73 - phase_6_search Query: 'Adjuster components and related products used in mechanical systems and assemblies. Includes manufacturing processes for precision adjustment devices, materials used in production, and typical applications in machinery, automotive, and industrial equipment. Focus on lifecycle stages such as production, usage, and disposal, with attention to material properties and industry standards.'
2025-05-28 11:34:56.482 | INFO     | emissions_factor_matching.dataset:search_candidates:74 - phase_6_search Filters: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}]}
2025-05-28 11:34:56.483 | INFO     | emissions_factor_matching.dataset:search_candidates:75 - phase_6_search Requesting 25 results
2025-05-28 11:34:59.710 | INFO     | emissions_factor_matching.dataset:search_candidates:97 - phase_6_search ChromaDB search completed in 3225.20ms
2025-05-28 11:34:59.711 | INFO     | emissions_factor_matching.dataset:search_candidates:98 - phase_6_search Retrieved 25 candidates
2025-05-28 11:34:59.714 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 1: 'electronic component machinery production, unspecified' (distance: 0.2996, similarity: 0.7004)
2025-05-28 11:34:59.718 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 2: 'inductor production, auxilliaries and energy use' (distance: 0.3293, similarity: 0.6707)
2025-05-28 11:34:59.721 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 3: 'steel turning, primarily dressing, computer numerical controlled' (distance: 0.3341, similarity: 0.6659)
2025-05-28 11:34:59.722 | INFO     | emissions_factor_matching.dataset:search_candidates:165 - phase_6_search Successfully created 25 candidate objects
2025-05-28 11:34:59.726 | INFO     | emissions_factor_matching.dataset:search_candidates:166 - phase_6_search Best match: 'electronic component machinery production, unspecified' (distance: 0.2996)
2025-05-28 11:34:59.728 | INFO     | emissions_factor_matching.dataset:search_candidates_with_fallback:191 - phase_6_fallback Primary search successful with 25 candidates
2025-05-28 11:34:59.730 | INFO     | emissions_factor_matching.api:get_recommended_activities:284 - Phase 6 Complete: Retrieved 25 candidates
2025-05-28 11:34:59.731 | INFO     | emissions_factor_matching.api:get_recommended_activities:287 - Phase 7: LLM Re-ranking & Justification - Starting
2025-05-28 11:35:06.834 | INFO     | completions:get_structured_completion:119 - Structured completion successful: RerankingResponse
2025-05-28 11:35:06.835 | INFO     | emissions_factor_matching.predictions:re_rank_candidates:695 - Re-ranking complete: Selected 'electronic component machinery production, unspecified' with MEDIUM confidence
2025-05-28 11:35:06.836 | INFO     | emissions_factor_matching.api:get_recommended_activities:299 - Phase 7 Complete: Selected 'electronic component machinery production, unspecified' with MEDIUM confidence
2025-05-28 11:35:06.836 | INFO     | emissions_factor_matching.api:get_recommended_activities:302 - Phase 8: Geography Matching & Record Retrieval - Starting
2025-05-28 11:35:06.847 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'electronic component machinery production, unspecified', using first available
2025-05-28 11:35:06.848 | INFO     | emissions_factor_matching.api:get_recommended_activities:310 - Phase 8 Complete: Geography matched to 'GLO' with source 'Ecoinvent 3.11'
2025-05-28 11:35:06.848 | INFO     | emissions_factor_matching.api:get_recommended_activities:313 - Phase 9: Response Assembly - Starting
2025-05-28 11:35:06.858 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'inductor production, auxilliaries and energy use', using first available
2025-05-28 11:35:06.879 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'steel turning, primarily dressing, computer numerical controlled', using first available
2025-05-28 11:35:06.900 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'steel turning, primarily roughing, computer numerical controlled', using first available
2025-05-28 11:35:06.919 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'brass turning, primarily dressing, computer numerical controlled', using first available
2025-05-28 11:35:06.938 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'ventilation duct production, connection piece, steel, 100x50 mm', using first available
2025-05-28 11:35:06.957 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'metal working machine production, unspecified', using first available
2025-05-28 11:35:06.976 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'brass turning, primarily roughing, computer numerical controlled', using first available
2025-05-28 11:35:06.996 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'energy and auxilliary inputs, metal working machine, with process heat from heavy fuel oil', using first available
2025-05-28 11:35:07.015 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'aluminium turning, primarily dressing, computer numerical controlled', using first available
2025-05-28 11:35:07.026 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'electronic component production, active, unspecified', using first available
2025-05-28 11:35:07.049 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'aluminium turning, primarily roughing, computer numerical controlled', using first available
2025-05-28 11:35:07.068 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'brass turning, average, computer numerical controlled', using first available
2025-05-28 11:35:07.094 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'energy and auxilliary inputs, metal working machine, with process heat from natural gas', using first available
2025-05-28 11:35:07.117 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'steel turning, average, computer numerical controlled', using first available
2025-05-28 11:35:07.140 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'industrial machine production, heavy, unspecified', using first available
2025-05-28 11:35:07.164 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'deep drawing, steel, 3500 kN press, automode', using first available
2025-05-28 11:35:07.187 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'exhaust air outlet production, steel/aluminium, 85x365 mm', using first available
2025-05-28 11:35:07.210 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'electronics production, for control units', using first available
2025-05-28 11:35:07.242 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'energy and auxilliary inputs, metal working machine, with process heat from light fuel oil', using first available
2025-05-28 11:35:07.267 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'deep drawing, steel, 38000 kN press, automode', using first available
2025-05-28 11:35:07.293 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'cast iron turning, primarily roughing, computer numerical controlled', using first available
2025-05-28 11:35:07.318 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'energy and auxilliary inputs, metal working factory, with heating from natural gas', using first available
2025-05-28 11:35:07.353 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'aluminium turning, average, computer numerical controlled', using first available
2025-05-28 11:35:07.383 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:155 - Preferred source 'Unknown' not found for activity 'exhaust air valve production, in-wall housing, plastic/steel, DN 125', using first available
2025-05-28 11:35:07.385 | INFO     | emissions_factor_matching.api:get_recommended_activities:332 - Phase 9 Complete: Assembled response with 24 recommendations
2025-05-28 11:35:07.409 | INFO     | utils.cache:wrapper:347 - Cache miss for get_recommended_activities:{"args": [], "kwargs": {"activity_request": {"casNo": null, "cas_number": null, "chemicalName": "adjuster", "chemical_name": null, "geography": null, "geographyModeling": null, "iso_code": null, "labs": null, "lcaLifecycleStage": null, "productCategory": "Backpacks", "product_category": null, "unit": null}, "api_version": "latest"}}, cached result with expiry 31536000s
2025-05-28 11:35:07.436 | INFO     | emissions_factor_matching.tests.test_api:test_smoke_test_activity_recommendations_with_product_category:59 - {'matched_activity': {'activity_uuid': '01d972e4-77bf-5873-8dc7-aaf9ed9f1b64', 'activity_name': 'electronic component machinery production, unspecified', 'reference_product_name': 'electronic component machinery, unspecified', 'product_information': 'This is an infrastructure, representing the product of an electronic component machinery, unspecified. The product represents a 2,500 kg  machinery that is used in the production of semiconductors, printed wiring boards, capacitors, etc. and it is based on an average of wafer marking system, laser blind-via drilling system and a IC trim system. The process includes input  material, production efforts and infrastructure.', 'source': 'Ecoinvent 3.11', 'geography': 'GLO'}, 'confidence': 'MEDIUM', 'explanation': "The selected candidate, 'electronic component machinery production, unspecified,' aligns moderately well with the user's request for adjuster components and related products. While the activity is not explicitly about adjusters, the production of electronic component machinery could involve precision adjustment devices and related manufacturing processes. The unspecified nature of the product and activity introduces some uncertainty, but the general focus on machinery production is relevant to the user's context. The geographic and temporal details are not specified, which limits the confidence level.", 'recommendations': [{'activity_uuid': 'f438b999-7b91-50dc-a06f-eeae3f083e2b', 'activity_name': 'inductor production, auxilliaries and energy use', 'reference_product_name': 'inductor, auxilliaries and energy use', 'product_information': 'This is delivering the burdens of energy and auxiliary material requirements for the production of 1 kg of inductors over all types of inductors. It includes data for energy, emission, end-of-life, infrastructure. This should be used in combination with the materials used for the production of the component.', 'source': 'Ecoinvent 3.11', 'geography': 'GLO', 'similarity': 0.6707117557525635}, {'activity_uuid': 'df22f831-f00d-5387-8837-c0f06114a3b8', 'activity_name': 'steel turning, primarily dressing, computer numerical controlled', 'reference_product_name': 'steel removed by turning, primarily dressing, computer numerical controlled', 'product_information': "This is delivering the service of 'steel removed by turning, primarily dressing, computer numerical controlled'. Turning is a chipping process where the piece of material is rotating while the cutting tool is not. It can be applied to both inside and outside of the piece to be machined and it is normally used to produce cylindrical shapes. It is performed in metals, wood and plastic materials. When the process is performed with dressing operation, then only minor amounts of materials are removed to produce the exact dimensions and smooth surface. This dataset represents the turning of 1 kg of steel. The service includes materials input, production infrastructure, machinery and electricity consumption. Degreasing is not included and has to be added if necessary.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.665897786617279}, {'activity_uuid': '302fc909-25bb-547a-b828-9df0d9a91977', 'activity_name': 'steel turning, primarily roughing, computer numerical controlled', 'reference_product_name': 'steel removed by turning, primarily roughing, computer numerical controlled', 'product_information': "This is delivering the service of 'steel removed by turning, primarily roughing, computer numerical controlled'. Turning is a chipping process where the piece of material is rotating while the cutting tool is not. It can be applied to both inside and outside of the piece to be machined and it is normally used to produce cylindrical shapes. It is performed in metals, wood and plastic materials. When the process is performed with roughing operation, then large amounts of materials are removed at once. The process is computer numerical controlled, meaning that it operates autonomously once programmed. This dataset represents the turning of 1 kg of steel. The service includes materials input, production infrastructure, machinery and electricity consumption. Degreasing is not included and has to be added if necessary.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6647314429283142}, {'activity_uuid': 'ce9f06cb-8a4c-54cd-8e84-9d772bd22b49', 'activity_name': 'brass turning, primarily dressing, computer numerical controlled', 'reference_product_name': 'brass removed by turning, primarily dressing, computer numerical controlled', 'product_information': "This is delivering the service of 'brass removed by turning, primarily dressing, computer numerical controlled'. Turning is a chipping process where the piece of material is rotating while the cutting tool is not. It can be applied to both inside and outside of the piece to be machined and it is normally used to produce cylindrical shapes. It is performed in metals, wood and plastic materials. When the process is performed with dressing operation, then only minor amounts of materials are removed to produce the exact dimensions and smooth surface. The process is computer numerical controlled, meaning that it operates autonomously once programmed. This dataset represents the turning of 1 kg of brass. The service includes materials input, production infrastructure, machinery and electricity consumption. Degreasing is not included and has to be added if necessary.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6630720794200897}, {'activity_uuid': '54266e0c-51cb-5751-9c27-68e9c746b81f', 'activity_name': 'ventilation duct production, connection piece, steel, 100x50 mm', 'reference_product_name': 'ventilation duct, connection piece, steel, 100x50 mm', 'product_information': 'This is an infrastructure, representing the product of a ventilation duct, connection piece, steel, 100x50 mm, a typical component used for a ventilation system in a multi family house. It is made of steel and has a lifetime of 50 years. The process includes the input materials, energy consumption during the production and production infrastructure.', 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6618296205997467}, {'activity_uuid': '6d46aa82-ce35-5c58-8b5e-bb5a51456a01', 'activity_name': 'metal working machine production, unspecified', 'reference_product_name': 'metal working machine, unspecified', 'product_information': 'This is an infrastructure, representing the product of a metal working machine, unspecified. 1 kg of this process is needed for 1 kg of machine.The total weigh of the machine and the amount of the products produced over its lifetime depend heavily on the production pattern. Thus, the process represents an unspecified metal machine. It includes all materials necessary to construct it, energy consumption during manufacturing and decommission.', 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6594079434871674}, {'activity_uuid': '6d89ddd3-e71c-5608-8117-************', 'activity_name': 'brass turning, primarily roughing, computer numerical controlled', 'reference_product_name': 'brass removed by turning, primarily roughing, computer numerical controlled', 'product_information': "This is delivering the service of 'brass removed by turning, primarily roughing, computer numerical controlled'. Turning is a chipping process where the piece of material is rotating while the cutting tool is not. It can be applied to both inside and outside of the piece to be machined and it is normally used to produce cylindrical shapes. It is performed in metals, wood and plastic materials. When the process is performed with roughing operation, then large amounts of materials are removed at once. The process is computer numerical controlled, meaning that it operates autonomously once programmed. This dataset represents the turning of 1 kg of brass. The service includes materials input, production infrastructure, machinery and electricity consumption. Degreasing is not included and has to be added if necessary.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.652074545621872}, {'activity_uuid': 'c8fd4ac7-ae6c-575e-8590-c8a44481dfdf', 'activity_name': 'energy and auxilliary inputs, metal working machine, with process heat from heavy fuel oil', 'reference_product_name': 'energy and auxilliary inputs, metal working machine', 'product_information': "This is delivering the service of 'energy and auxilliary inputs, metal working machine'. The service represents the energy and auxilliary processes needed for a metal working machine to operate. 1 kg of this process is needed to produce 1 kg of final product. It includes input materials, energy consumption, emissions, materials end-of-life and process heat coming form natural gas, heavy fuel oil, light fuel oil or hard coal.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.65126633644104}, {'activity_uuid': '444d1805-6f23-52e1-a74b-8e2727d73596', 'activity_name': 'aluminium turning, primarily dressing, computer numerical controlled', 'reference_product_name': 'aluminium removed by turning, primarily dressing, computer numerical controlled', 'product_information': "This is delivering the service of 'aluminium removed by turning, primarily dressing, computer numerical controlled'. Turning is a chipping process where the piece of material is rotating while the cutting tool is not. It can be applied to both inside and outside of the piece to be machined and it is normally used to produce cylindrical shapes. It is performed in metals, wood and plastic materials. When the process is performed with dressing operation, then only minor amounts of materials are removed to produce the exact dimensions and smooth surface. The process is computer numerical controlled, meaning that it operates autonomously once programmed. This dataset represents the turning of 1 kg of aluminium. The service includes materials input, production infrastructure, machinery and electricity consumption. Degreasing is not included and has to be added if necessary.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6495010852813721}, {'activity_uuid': '64162636-813a-5873-9ced-0de41fc1b40f', 'activity_name': 'electronic component production, active, unspecified', 'reference_product_name': 'electronic component, active, unspecified', 'product_information': 'This product represents an unweighted average between the following datasets of active components: diodes, transistors and integrated circuits. It is intended to be used as a rough proxy for active components, if no further data is available.', 'source': 'Ecoinvent 3.11', 'geography': 'GLO', 'similarity': 0.6482097208499908}, {'activity_uuid': '972e06c6-d501-5f29-9784-f075513b5e8b', 'activity_name': 'aluminium turning, primarily roughing, computer numerical controlled', 'reference_product_name': 'aluminium removed by turning, primarily roughing, computer numerical controlled', 'product_information': "This is delivering the service of 'aluminium removed by turning, primarily roughing, computer numerical controlled'. Turning is a chipping process where the piece of material is rotating while the cutting tool is not. It can be applied to both inside and outside of the piece to be machined and it is normally used to produce cylindrical shapes. It is performed in metals, wood and plastic materials. When the process is performed with roughing operation, then large amounts of materials are removed at once. The process is computer numerical controlled, meaning that it operates autonomously once programmed. This dataset represents the turning of 1 kg of aluminium. The service includes materials input, production infrastructure, machinery and electricity consumption. Degreasing is not included and has to be added if necessary.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6446568965911865}, {'activity_uuid': '671ced98-9078-55af-9a40-a7342da7ccc4', 'activity_name': 'brass turning, average, computer numerical controlled', 'reference_product_name': 'brass removed by turning, average, computer numerical controlled', 'product_information': "This is delivering the service of 'brass removed by turning, average, computer numerical controlled'. Turning is a chipping process where the piece of material is rotating while the cutting tool is not. It can be applied to both inside and outside of the piece to be machined and it is normally used to produce cylindrical shapes. It is performed in metals, wood and plastic materials. The process is computer numerical controlled, meaning that it operates autonomously once programmed. This dataset represents the turning of 1 kg of brass. The service includes materials input, production infrastructure, machinery and electricity consumption. Degreasing is not included and has to be added if necessary.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6432079672813416}, {'activity_uuid': 'f2a91f45-47cb-5826-9cb5-3fb792d63b54', 'activity_name': 'energy and auxilliary inputs, metal working machine, with process heat from natural gas', 'reference_product_name': 'energy and auxilliary inputs, metal working machine', 'product_information': "This is delivering the service of 'energy and auxilliary inputs, metal working machine'. The service represents the energy and auxilliary processes needed for a metal working machine to operate. 1 kg of this process is needed to produce 1 kg of final product. It includes input materials, energy consumption, emissions, materials end-of-life and process heat coming form natural gas, heavy fuel oil, light fuel oil or hard coal.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6422757506370544}, {'activity_uuid': 'e3039457-5cbe-56fe-bf1e-b627e60faa9e', 'activity_name': 'steel turning, average, computer numerical controlled', 'reference_product_name': 'steel removed by turning, average, computer numerical controlled', 'product_information': "This is delivering the service of 'steel removed by turning, average, computer numerical controlled'. Turning is a chipping process where the piece of material is rotating while the cutting tool is not. It can be applied to both inside and outside of the piece to be machined and it is normally used to produce cylindrical shapes. It is performed in metals, wood and plastic materials. The process is computer numerical controlled, meaning that it operates autonomously once programmed. This dataset represents the turning of 1 kg of steel. The service includes materials input, production infrastructure, machinery and electricity consumption. Degreasing is not included and has to be added if necessary.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6420496702194214}, {'activity_uuid': '6ea8dae1-9201-511f-97ab-ef1d78ee5f6f', 'activity_name': 'industrial machine production, heavy, unspecified', 'reference_product_name': 'industrial machine, heavy, unspecified', 'product_information': 'This is an infrastructure, representing the product of a industrial machine, heavy, unspecified. The infrastructure represents a rock crusher made with a lifetime of 25 years. The process includes input materials but excludes the energy for assebling. ', 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6395434141159058}, {'activity_uuid': 'ce362d8b-47a6-5645-b290-fe5ea81e52c1', 'activity_name': 'deep drawing, steel, 3500 kN press, automode', 'reference_product_name': 'deep drawing, steel, 3500 kN press, automode', 'product_information': "This is delivering the service of 'deep drawing, steel, 3500 kN press, automode'. Deep drawing in the technology of stretching a sheet metal (called blank) into a hollow shape. A punch presses the blank through the die cavity that defines the shape. The service represents the deep drawing of steel with 3500 kN press in automode and includes energy consumption, production infrastructure and machinery, but it excludes the steel input. Degreasing is also not included, so it has to be added if necessary. Deep drawing steel is used when complex geometries with straight sides are required in the end product, like automotive industry.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6379461884498596}, {'activity_uuid': '76004994-8a51-5304-86d8-97886123ee9f', 'activity_name': 'exhaust air outlet production, steel/aluminium, 85x365 mm', 'reference_product_name': 'exhaust air outlet, steel/aluminium, 85x365 mm', 'product_information': 'This is an infrastructure, representing the product of an exhaust air outlet, steel/aluminium, 85x365 mm, a typical component used for a ventilation system in a multi family house. It is compatible with spiral seam duct of 125 mm diameter and it has a mass of 2.5 kg per element. The process includes all materials necessary to construct it, energy consumption during manufacturing and production infrastructure.  ', 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6370014250278473}, {'activity_uuid': '9aeba2c0-594c-5bbd-a035-ba6976ee402c', 'activity_name': 'electronics production, for control units', 'reference_product_name': 'electronics, for control units', 'product_information': 'This product represents generic electronics for a control unit. It has a composition of 46% steel (housing), 32% plastics, 14% printed wiring boards and 8% cables (various types).', 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6365706026554108}, {'activity_uuid': '227d12d5-5ca3-53b0-b8ab-23bbde9d8750', 'activity_name': 'energy and auxilliary inputs, metal working machine, with process heat from light fuel oil', 'reference_product_name': 'energy and auxilliary inputs, metal working machine', 'product_information': "This is delivering the service of 'energy and auxilliary inputs, metal working machine'. The service represents the energy and auxilliary processes needed for a metal working machine to operate. 1 kg of this process is needed to produce 1 kg of final product. It includes input materials, energy consumption, emissions, materials end-of-life and process heat coming form natural gas, heavy fuel oil, light fuel oil or hard coal.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.635945200920105}, {'activity_uuid': '9bf1bae0-438e-5212-a475-62dda3707ab6', 'activity_name': 'deep drawing, steel, 38000 kN press, automode', 'reference_product_name': 'deep drawing, steel, 38000 kN press, automode', 'product_information': "This is delivering the service of 'deep drawing, steel, 38000 kN press, automode'. Deep drawing in the technology of stretching a sheet metal (called blank) into a hollow shape. A punch presses the blank through the die cavity that defines the shape. The service represents the deep drawing of steel with 38000 kN press in automode and includes energy consumption, production infrastructure and machinery, but it excludes the steel input. Degreasing is also not included, so it has to be added if necessary. Deep drawing steel is used when complex geometries with straight sides are required in the end product, like automotive industry.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6339460611343384}, {'activity_uuid': 'fb11e3a0-21bd-5e66-b9dc-49cc2b51a649', 'activity_name': 'cast iron turning, primarily roughing, computer numerical controlled', 'reference_product_name': 'cast iron removed by turning, primarily roughing, computer numerical controlled', 'product_information': "This is delivering the service of 'cast iron removed by turning, primarily roughing, computer numerical controlled'. Turning is a chipping process where the piece of material is rotating while the cutting tool is not. It can be applied to both inside and outside of the piece to be machined and it is normally used to produce cylindrical shapes. It is performed in metals, wood and plastic materials. When the process is performed with roughing operation, then large amounts of materials are removed at once. The process is computer numerical controlled, meaning that it operates autonomously once programmed. This dataset represents the turning of 1 kg of cast iron. The service includes materials input, production infrastructure, machinery and electricity consumption. Degreasing is not included and has to be added if necessary.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6323672831058502}, {'activity_uuid': 'f90b2353-3fdd-50da-b2d1-c457a1fc3f39', 'activity_name': 'energy and auxilliary inputs, metal working factory, with heating from natural gas', 'reference_product_name': 'energy and auxilliary inputs, metal working factory', 'product_information': "This is delivering the service of 'energy and auxilliary inputs, metal working factory'. The service represents the energy and auxilliary processes needed for a metal working factory to operate. 1 kg of this process is needed to produce 1 kg of final product. It includes electricity for lighting, general water consumption, heat consumption from natural gas, heavy fuel oil, light fuel oil or hard coal, wastewater treatment and materials end-of-life.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6322664022445679}, {'activity_uuid': '1d4ebf5e-6c73-594b-ad64-00d0f6a21742', 'activity_name': 'aluminium turning, average, computer numerical controlled', 'reference_product_name': 'aluminium removed by turning, average, computer numerical controlled', 'product_information': "This is delivering the service of 'aluminium removed by turning, average, computer numerical controlled'. Turning is a chipping process where the piece of material is rotating while the cutting tool is not. It can be applied to both inside and outside of the piece to be machined and it is normally used to produce cylindrical shapes. It is performed in metals, wood and plastic materials. The process is computer numerical controlled, meaning that it operates autonomously once programmed. This dataset represents the turning of 1 kg of aluminium. The service includes materials input, production infrastructure, machinery and electricity consumption. Degreasing is not included and has to be added if necessary.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6319966018199921}, {'activity_uuid': 'd87f87f5-dbbe-5d36-8195-3ddf191db207', 'activity_name': 'exhaust air valve production, in-wall housing, plastic/steel, DN 125', 'reference_product_name': 'exhaust air valve, in-wall housing, plastic/steel, DN 125', 'product_information': 'This is an infrastructure, representing the product of an exhaust air valve, in-wall housing, plastic/steel, DN 125, a typical component used for a ventilation system in a multi family house. It is usually mounted in room ceiling, it is compatible with ducts of 40-120 cm2 section area and it has a mass of 0.5 kg per element. The process includes all materials necessary to construct it, energy consumption during manufacturing and production infrastructure.  ', 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.6318862736225128}]}
.2025-05-28 11:35:07.443 | WARNING  | utils.cache:_get_all_keys:212 - _get_all_keys called on RedisCache - this operation can be expensive
2025-05-28 11:35:07.449 | INFO     | emissions_factor_matching.tests.test_api:setUp:23 - Cache clear response: 200
2025-05-28 11:35:07.454 | INFO     | emissions_factor_matching.api:search_activities:122 - API Version: latest
2025-05-28 11:35:09.430 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: polychloroprene production
2025-05-28 11:35:09.431 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:09.457 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - DE
2025-05-28 11:35:09.458 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - US-LA
2025-05-28 11:35:09.459 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - JP
2025-05-28 11:35:09.459 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - CN
2025-05-28 11:35:09.491 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: polyether polyols production, long chain, ISOPA
2025-05-28 11:35:09.492 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:09.493 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:09.549 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: polyether polyols production, short chain, ISOPA
2025-05-28 11:35:09.550 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:09.551 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:09.625 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: pelletising of polyvinylchloride
2025-05-28 11:35:09.626 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:09.627 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:09.649 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: pelletising of polyvinylchloride
2025-05-28 11:35:09.649 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:09.651 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:09.734 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: pelletising of polypropylene
2025-05-28 11:35:09.736 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:09.737 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:09.816 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: pelletising of polyethylene/polypropylene
2025-05-28 11:35:09.817 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:09.818 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:09.988 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: pelletising of polyethylene, low density
2025-05-28 11:35:09.989 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:09.990 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:10.013 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: pelletising of polyethylene, high density
2025-05-28 11:35:10.013 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:10.015 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:10.089 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: pelletising of polyethylene, low density, coloured
2025-05-28 11:35:10.094 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:10.099 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:10.160 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: polyvinylchloride, rigid, pellets, recycled to generic market for polyvinylchloride
2025-05-28 11:35:10.161 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:10.162 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:10.193 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: polymethyl methacrylate production
2025-05-28 11:35:10.194 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:10.195 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RAS
2025-05-28 11:35:10.196 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:10.197 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RNA
2025-05-28 11:35:10.229 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:164 - No good match for: polyvinylchloride, flexible, pellets, recycled to generic market for polyvinylchloride
2025-05-28 11:35:10.231 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:165 - Geographies in ecoinvent
2025-05-28 11:35:10.232 | WARNING  | emissions_factor_matching.geography:get_geography_activity_match:168 - RER
2025-05-28 11:35:10.349 | INFO     | utils.cache:wrapper:347 - Cache miss for search_activities:{"args": [], "kwargs": {"api_version": "latest", "geography_iso3": "GLO", "number_of_results": 50, "query": "non-ionic polyol"}}, cached result with expiry 31536000s
2025-05-28 11:35:10.361 | INFO     | emissions_factor_matching.tests.test_api:test_smoke_test_search_activities:34 - [{'activity_uuid': 'f2e4f4d4-47b1-52c4-9eb3-ce3f38efdd3e', 'activity_name': 'polyvinyl chloride production, emulsion polymerisation', 'reference_product_name': 'polyvinyl chloride, emulsion polymerised', 'product_information': "'polyvinylchloride, emulsion polymerised', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: Building products (window frames and other profiles, floor and wall coverings, roofing sheets, linings for tunnels, swimming-pools and reservoirs), piping (water and sewerage pipes and fittings, and ducts for power and telecommunications), coatings (tarpaulins, rainwear and corrugated metal sheets), insulation and sheathing for low voltage power supplies, telecommunications, appliances, and automotive applications, packaging, pharmaceuticals, food and confectionery, water and fruit juices, labels, presentation trays, automotive applications (cables, underbody coating and interior trimmings), medical products (blood bags, transfusion tubes and surgical gloves), leisure products (garden hoses, footwear, inflatable pools, tents).     ", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '1a3c8374-a9a6-5acb-bd09-1c8093da41ac', 'activity_name': 'non-ionic surfactant production, ethylene oxide derivate', 'reference_product_name': 'non-ionic surfactant', 'product_information': "non-ionic surfactant' represent the entire substance group of non-ionic surfactants. This type accounts for 40% of global demand. Of the non-ionic surfactants, the main groups are: Ethoxylated Linear Alcohols (40%), Fatty Acid Esters (20%), Ethoxylated Alkyl Phenols (15%) and Amine and Amide Derivatives (10%). The product group is represented by a mixture of an ethoxylated alcohol surfactant based on dodecanol and a fatty acid derived surfactant. It is liquid under normal conditions of temperature and pressure. It is modelled as a pure substance. Modelling this substance in a solution requires the user to add the solvent of their choice in their models. On a consumer level, is used in the following products: cosmetic products. On industrial sites, the substance is used for the manufacture of products in the following sectors: pharmaceutical and food industry.", 'source': 'Ecoinvent 3.11', 'geography': 'GLO', 'similarity': 0.0}, {'activity_uuid': 'b6b6854a-b062-536c-8c72-1a2d7462ca4a', 'activity_name': 'polyvinyl chloride production, suspension polymerisation', 'reference_product_name': 'polyvinyl chloride, suspension polymerised', 'product_information': "'polyvinylchloride, suspension polymerised', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: Building products (window frames and other profiles, floor and wall coverings, roofing sheets, linings for tunnels, swimming-pools and reservoirs), piping (water and sewerage pipes and fittings, and ducts for power and telecommunications), coatings (tarpaulins, rainwear and corrugated metal sheets), insulation and sheathing for low voltage power supplies, telecommunications, appliances, and automotive applications, packaging, pharmaceuticals, food and confectionery, water and fruit juices, labels, presentation trays, automotive applications (cables, underbody coating and interior trimmings), medical products (blood bags, transfusion tubes and surgical gloves), leisure products (garden hoses, footwear, inflatable pools, tents).", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '21ad5ecd-ea30-5671-a15a-2a008cac6735', 'activity_name': 'polyether polyols production, short chain', 'reference_product_name': 'polyether polyols, short chain', 'product_information': "polyether polyols, short chain' represents an organic substance with a generic molecular formula of: HO--(AO)m ZO--(BO)n H  whereas A, B and Z are organic rests. It is liquid under normal conditions of temperature and pressure with a molecular weight under 1000 g/mol. There are no publicly available information about the consumption of this substance on a consumer level. On industrial sites, the substance is used with MDI to produce rigid polyurethane foams.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '392bc9d9-a1d8-5179-a9a8-b67c950d8fe7', 'activity_name': 'polyether polyols production, long chain', 'reference_product_name': 'polyether polyols, long chain', 'product_information': "polyether polyols, long chain' represents an organic substance with a generic molecular formula of: HO--(AO)m ZO--(BO)n H  whereas A, B and Z are organic rests. It is waxy solid under normal conditions of temperature and pressure with a molecular weight over 1000 g/mol There are no publicly available information about the consumption of this substance on a consumer level. On industrial sites, the substance is used with TDI to produce flexible polyurethane foams.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '487738ee-41ce-5824-b9cf-1bbbc21b64be', 'activity_name': 'polychloroprene production', 'reference_product_name': 'polychloroprene', 'product_information': "''polychloroprene', is a plastic product of fossil origin, it is not biodegradable and it is a synthetic rubber (elastomer) material. This product consists of 100% virgin material with no content of recycled material.", 'source': 'Ecoinvent 3.11', 'geography': 'DE', 'similarity': 0.0}, {'activity_uuid': '6ebffe7f-a6c0-52d8-8637-5539335a38fe', 'activity_name': 'polyether polyols production, long chain, ISOPA', 'reference_product_name': 'polyether polyols, long chain, ISOPA', 'product_information': "polyether polyols, long chain, ISOPA' represents an organic substance with a generic molecular formula of: HO--(AO)m ZO--(BO)n H  whereas A, B and Z are organic rests. It is waxy solid under normal conditions of temperature and pressure with a molecular weight over 1000 g/mol There are no publicly available information about the consumption of this substance on a consumer level. On industrial sites, the substance is used with TDI to produce flexible polyurethane foams. This exchange is only produced in a pre-allocated dataset based on industry data from ISOPA (European trade association for producers of diisocyanates and polyols).", 'source': 'Ecoinvent 3.11', 'geography': 'RER', 'similarity': 0.0}, {'activity_uuid': 'b509406c-c98f-51b1-95bb-96fcd2395589', 'activity_name': 'non-ionic surfactant production, fatty acid derivate', 'reference_product_name': 'non-ionic surfactant', 'product_information': "non-ionic surfactant' represent the entire substance group of non-ionic surfactants. This type accounts for 40% of global demand. Of the non-ionic surfactants, the main groups are: Ethoxylated Linear Alcohols (40%), Fatty Acid Esters (20%), Ethoxylated Alkyl Phenols (15%) and Amine and Amide Derivatives (10%). The product group is represented by a mixture of an ethoxylated alcohol surfactant based on dodecanol and a fatty acid derived surfactant. It is liquid under normal conditions of temperature and pressure. It is modelled as a pure substance. Modelling this substance in a solution requires the user to add the solvent of their choice in their models. On a consumer level, is used in the following products: cosmetic products. On industrial sites, the substance is used for the manufacture of products in the following sectors: pharmaceutical and food industry.", 'source': 'Ecoinvent 3.11', 'geography': 'GLO', 'similarity': 0.0}, {'activity_uuid': 'c6d2aeae-526e-5151-803f-5d40cda45807', 'activity_name': 'anionic resin production', 'reference_product_name': 'anionic resin', 'product_information': "'anionic resin', is a plastic product of fossil origin, it is not biodegradable and it is a thermoset material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: water-soluble or water dispersible adhesives, textile auxiliaries, paper auxiliaries, leather auxiliaries, and deodorants for UF resins.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '46282283-1d6c-55f7-875e-e9583cfe0507', 'activity_name': 'polyether polyols production, short chain, ISOPA', 'reference_product_name': 'polyether polyols, short chain, ISOPA', 'product_information': "polyether polyols, short chain, ISOPA' represents an organic substance with a generic molecular formula of: HO--(AO)m ZO--(BO)n H  whereas A, B and Z are organic rests. It is liquid under normal conditions of temperature and pressure with a molecular weight under 1000 g/mol. There are no publicly available information about the consumption of this substance on a consumer level. On industrial sites, the substance is used with MDI to produce rigid polyurethane foams. This exchange is only produced in a pre-allocated dataset based on industry data from ISOPA (European trade association for producers of diisocyanates and polyols).", 'source': 'Ecoinvent 3.11', 'geography': 'RER', 'similarity': 0.0}, {'activity_uuid': 'c59667a8-d07d-5e4d-9636-f6758894b306', 'activity_name': 'polyvinyl chloride production, unspecified polymerisation, weighted average', 'reference_product_name': 'polyvinyl chloride, unspecified polymerisation, weighted average', 'product_information': "polyvinyl chloride, unspecified polymerisation, weighted average', describes a plastic product of fossil origin with an unspecified polymerization process. It is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: Building products (window frames and other profiles, floor and wall coverings, roofing sheets, linings for tunnels, swimming-pools and reservoirs), piping (water and sewerage pipes and fittings, and ducts for power and telecommunications), coatings (tarpaulins, rainwear and corrugated metal sheets), insulation and sheathing for low voltage power supplies, telecommunications, appliances, and automotive applications, packaging, pharmaceuticals, food and confectionery, water and fruit juices, labels, presentation trays, automotive applications (cables, underbody coating and interior trimmings), medical products (blood bags, transfusion tubes and surgical gloves), leisure products (garden hoses, footwear, inflatable pools, tents).\n   ", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '28ac450d-2938-5e7c-828a-59916cca1926', 'activity_name': 'cationic resin production', 'reference_product_name': 'cationic resin', 'product_information': "'cationic resin', is a plastic product of fossil origin, it is not biodegradable and it is a thermoset material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: water purification, water softening, wastewater treatment, purification of pharmaceuticals and food, catalysis.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '38101fef-d095-562b-aca9-876a2e8309f1', 'activity_name': 'polydimethylsiloxane production', 'reference_product_name': 'polydimethylsiloxane', 'product_information': 'Polydimethylsiloxane is a silicon oil. This product can be both sold in oil and water emulsions (Kakhia n.d.). Silicon oils are generally used as lubricants for industrial and military applications, given their high usability in combination with aluminum, brass and bronze. Silicone oils can also be used in the production of lubricating greases (Mang et al. 2011).', 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'a23e3aab-4be3-55ef-b376-93dfe81d3876', 'activity_name': 'pelletising of polyvinylchloride', 'reference_product_name': 'polyvinylchloride, rigid, pellets, recycled', 'product_information': "polyvinylchloride, rigid, pellets, recycled', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% recycled material. The product is used in the following applications and sectors: Building products (window frames and other profiles, floor and wall coverings, roofing sheets, linings for tunnels, swimming-pools and reservoirs), piping (water and sewerage pipes and fittings, and ducts for power and telecommunications), coatings (tarpaulins, rainwear and corrugated metal sheets), insulation and sheathing for low voltage power supplies, telecommunications, appliances, and automotive applications, packaging, pharmaceuticals, food and confectionery, water and fruit juices, labels, presentation trays, automotive applications (cables, underbody coating and interior trimmings), medical products (blood bags, transfusion tubes and surgical gloves), leisure products (garden hoses, footwear, inflatable pools, tents).\n   ", 'source': 'Ecoinvent 3.11', 'geography': 'RER', 'similarity': 0.0}, {'activity_uuid': 'a23e3aab-4be3-55ef-b376-93dfe81d3876', 'activity_name': 'pelletising of polyvinylchloride', 'reference_product_name': 'polyvinylchloride, flexible, pellets, recycled', 'product_information': "polyvinylchloride, flexible, pellets, recycled', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% recycled material. The product is used in the following applications and sectors: Building products (window frames and other profiles, floor and wall coverings, roofing sheets, linings for tunnels, swimming-pools and reservoirs), piping (water and sewerage pipes and fittings, and ducts for power and telecommunications), coatings (tarpaulins, rainwear and corrugated metal sheets), insulation and sheathing for low voltage power supplies, telecommunications, appliances, and automotive applications, packaging, pharmaceuticals, food and confectionery, water and fruit juices, labels, presentation trays, automotive applications (cables, underbody coating and interior trimmings), medical products (blood bags, transfusion tubes and surgical gloves), leisure products (garden hoses, footwear, inflatable pools, tents).\n   ", 'source': 'Ecoinvent 3.11', 'geography': 'RER', 'similarity': 0.0}, {'activity_uuid': 'ebeaebd4-50d3-58a9-86e9-511b03973eb1', 'activity_name': 'polyaluminium chloride production', 'reference_product_name': 'polyaluminium chloride', 'product_information': 'This product is mainly used for water treatment. This includes the use of polyaluminium chloride for treatment of drinking water, wastewater, industrial water and swimming pool water (DE DIETRICH SAS 2012).\n\nReference(s):\nGendorf (2016) Umwelterkl├ñrung 2015, Werk Gendorf Industriepark, www.gendorf.de\nDE DIETRICH SAS. 2012. The effective technology for water purification is PolyAluminium Chloride (PAC). Retrieved from:.researchgate.net%2Ffile.PostFileLoader.html%3Fid%3D559be0186307d9a9458b4567%26assetKey%3DAS%253A273808835973121%25401442292521157&usg=AFQjCNEn4gi4dcFB51Np02i8xJI-4CnxIg&sig2=S53kaaF9n4vVB7tpdbSqSQ&bvm=bv.141320020,d.d24, accessed 12Dec2016', 'source': 'Ecoinvent 3.11', 'geography': 'GLO', 'similarity': 0.0}, {'activity_uuid': '87c0fdd6-1f88-53cd-a920-dd07052f9551', 'activity_name': 'chemical production, inorganic', 'reference_product_name': 'chemical, inorganic', 'product_information': "'chemical, inorganic' describes an unweighted average of 20 pure, inorganic substances, being part of the top 100 chemicals and included into this database. The mix contains solid and liquid chemicals. Modelling this substance in a solution requires the user to add the solvent of their choice in their models. The main application is as reagents and intermediates in producing other chemicals.", 'source': 'Ecoinvent 3.11', 'geography': 'GLO', 'similarity': 0.0}, {'activity_uuid': '85e5d28e-3096-5d71-98b4-f2324146e10a', 'activity_name': 'polyester resin production, unsaturated', 'reference_product_name': 'polyester resin, unsaturated', 'product_information': "'polyester resin, unsaturated', is a plastic product of fossil origin, it is not biodegradable and it is a thermoset material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: coatings, adhesives, manufacturing of large glass fiber reinforced plastic articles like sanitary-ware, tanks, pipes, gratings, and high performance components for the marine and transportation industry such as closure panels, body panels, fenders, boat hulls and decks.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '423f9913-24eb-5d03-83e9-9f40bf37a861', 'activity_name': 'polyvinylfluoride production', 'reference_product_name': 'polyvinylfluoride', 'product_information': "'polyvinylfluoride', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: suitable for outdoor coatings or laminates, used as  flammability-lowering coatings of airplane interiors and photovoltaic module backsheets, in raincoats and metal sheeting.                                                                                                                                                                        .", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '80898a9f-97ea-595c-8e0a-ca2bb190adae', 'activity_name': 'polyacrylamide production', 'reference_product_name': 'polyacrylamide', 'product_information': "polyacrylamide'  is a polymer of fossil origin, the monomer with the CAS no. : 000079-06-1 is called 'prop-2-enamide' under IUPAC naming and its molecular formula is: C3H5NO. This product is 100% virgin material with no content of recycled material. It is predominately used in water treatment.", 'source': 'Ecoinvent 3.11', 'geography': 'GLO', 'similarity': 0.0}, {'activity_uuid': '26dc693b-65ee-5fe5-81e4-2716f3af5cae', 'activity_name': 'pelletising of polypropylene', 'reference_product_name': 'polypropylene, pellets, recycled', 'product_information': "polypropylene, pellets, recycled', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% recycled material. The product is used in the following applications and sectors: one of the top three widely used polymers today with extremely wide range of applications as plastic or as fibre, either transparent or pigmented, such as food packaging, textiles, automotive components, furniture market, medical devices, and consumer goods.", 'source': 'Ecoinvent 3.11', 'geography': 'RER', 'similarity': 0.0}, {'activity_uuid': '9dd960d9-afdf-50b6-b954-7239189ffa6b', 'activity_name': 'polyvinylfluoride production, dispersion', 'reference_product_name': 'polyvinylfluoride, dispersion', 'product_information': "'polyvinylfluoride, dispersion', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: suitable for outdoor coatings or laminates, used as  flammability-lowering coatings of airplane interiors and photovoltaic module backsheets, in raincoats and metal sheeting.   ", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'd24124ee-88b4-5404-baca-07f80c2a1051', 'activity_name': 'textile production, nonwoven polypropylene, spunbond', 'reference_product_name': 'textile, nonwoven polypropylene', 'product_information': 'This product represents a synthetic polypropylene textile made through a nonwoven process. It is unprocessed (without bleaching or dyeing) and can be used for the manufacturing of a large variety of products including clothes.', 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'fa8daec0-e814-542a-bfa1-a1b6428e3663', 'activity_name': 'acrylonitrile-butadiene-styrene copolymer production', 'reference_product_name': 'acrylonitrile-butadiene-styrene copolymer', 'product_information': "'acrylonitrile-butadiene-styrene copolymer', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: weight reduction offers improvement potentials in applications for e.g. automotive parts, household appliances or safety helmets.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'fc37f3ff-4027-5239-96dd-2718973b9455', 'activity_name': 'pelletising of polyethylene/polypropylene', 'reference_product_name': 'polyethylene/polypropylene, pellets, recycled', 'product_information': "polyethylene/polypropylene, pellets, recycled', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% recycled material. The product is used in the following applications and sectors: both polyethylene and polypropylene can be used for a wide array of application, e.g. manufacturing of plastic products", 'source': 'Ecoinvent 3.11', 'geography': 'RER', 'similarity': 0.0}, {'activity_uuid': '7d6b5b4f-99bc-5afe-8a37-ed6df0ce2aa3', 'activity_name': 'polyvinylidenchloride production, granulate', 'reference_product_name': 'polyvinylidenchloride, granulate', 'product_information': "polyvinylidenchloride, granulate', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: monofilaments for the production of military combat boots and insoles, draperies and curtains, and filter applications, medical applications, e.g., colostomy bags are made of monolayer or multilayer structures (EVA-PVDC-EVA), packaging film, packaging of food, drugs, cosmetics, and other perishable or delicate products to extend shelf life. ", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'd0d0122f-ec6f-4d23-b43d-644314f86fd0', 'activity_name': 'Polypropylene injection moulded hard plastic', 'reference_product_name': 'PP Hard plastic', 'product_information': 'Polypropylene injection moulded hard plastic', 'source': 'CarbonBright', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'b4c8bc0d-9e2f-5dcf-8b1e-49e092827be4', 'activity_name': 'polycarbonate production', 'reference_product_name': 'polycarbonate', 'product_information': "'polycarbonate', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: alternative for glass for applications (eyewear, bulletproof glass) building and construction sector (e.g., improving insulation and lightning), electrical and electronics (e.g., large flat screens and monitors, Digital Disks, consumer electronics equipment and fuse boxes as well as the large market of optical data storage), automotive industry (e.g., mirror and headlamp houses), domestic appliances, packaging, 3D printed models, medical devices as well as leisure and safety (e.g., goggles, helmet).\n", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '95dca35f-0d92-5430-bf2f-5a3bc8e7db53', 'activity_name': 'isophthalic acid based unsaturated polyester resin production', 'reference_product_name': 'isophthalic acid based unsaturated polyester resin', 'product_information': "'isophthalic acid based unsaturated polyester resin', is a plastic product of fossil origin, it is not biodegradable and it is a thermoset material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: chemical tanks/pipelines, fume extractor, duct, hood, chemical equipment, tanker, boat.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '3f9356c0-ff96-5935-867d-d6689fb8689a', 'activity_name': 'polyphenylene sulfide production', 'reference_product_name': 'polyphenylene sulfide', 'product_information': "'polyphenylene sulfide', is a plastic product of fossil origin. It is a precursor substance meant to be used for the further production of plastics. The product is used in the following applications and sectors: in automotive market as alternative for  metal, thermosets and other types of plastic, in more demanding applications, ideal choice for automotive parts exposed to high temperatures, automotive fluids, or mechanical stress.", 'source': 'Ecoinvent 3.11', 'geography': 'GLO', 'similarity': 0.0}, {'activity_uuid': '500ecaa7-356e-54c7-9d84-1d9b91fbc055', 'activity_name': 'dicyclopentadiene based unsaturated polyester resin production', 'reference_product_name': 'dicyclopentadiene based unsaturated polyester resin', 'product_information': "'dicyclopentadiene based unsaturated polyester resin', is a plastic product of fossil origin, it is not biodegradable and it is a thermoset material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: coatings, adhesives, manufacturing of large glass fiber reinforced plastic articles like sanitary-ware, tanks, pipes, gratings, and high performance components for the marine and transportation industry such as closure panels, body panels, fenders, boat hulls and decks.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'c2645ea1-dd48-595a-a60b-c9d613355658', 'activity_name': 'polymer foaming', 'reference_product_name': 'polymer foaming', 'product_information': "This is delivering the service of 'polymer foaming'. Foams are generated by adding physical (gases and low-boiling compounds) or chemical blowing agents (gas-releasing compounds) to the polymer. In principle, all polymers can be foamed but only a limited number of materials have been employed in the manufacture of foams, such as the thermoplastics polystyrene, poly(vinyl chloride), and low-density polyethylene (LDPE), as well as phenolic and polyurethane resins.                   In the service of foaming, 1 kg of input corresponds to 1 kg of expanded plastics. The converted amount of plastics is not included into the dataset. Thus, it should be used along 1 kg of plastic that can be foamed(usually polystyrene).", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'a92e54eb-e804-4a26-8483-f7404debbf4f', 'activity_name': 'Polystyrene injection moulded hard plastic', 'reference_product_name': 'PS Hard plastic', 'product_information': 'Polystyrene injection moulded hard plastic', 'source': 'CarbonBright', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'd84c54b1-a60a-5b98-bb5c-3ed4e8062b30', 'activity_name': 'orthophthalic acid based unsaturated polyester resin production', 'reference_product_name': 'orthophthalic acid based unsaturated polyester resin', 'product_information': "'orthophthalic acid based unsaturated polyester resin', is a plastic product of fossil origin, it is not biodegradable and it is a thermoset material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: coatings, adhesives, manufacturing of large glass fiber reinforced plastic articles like sanitary-ware, tanks, pipes, gratings, and high performance components for the marine and transportation industry such as closure panels, body panels, fenders, boat hulls and decks.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '8aa5ecd2-3502-5ee8-9a90-2cd363f7d30e', 'activity_name': 'pelletising of polyethylene, low density', 'reference_product_name': 'polyethylene, low density, pellets, recycled', 'product_information': "polyethylene, low density, pellets, recycled', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% recycled material. The product is used in the following applications and sectors: mainly used as films, e.g. for the production of plastic bags, packaging material, and agricultural films. It is also used for electrical cable coating.", 'source': 'Ecoinvent 3.11', 'geography': 'RER', 'similarity': 0.0}, {'activity_uuid': '3de70ddd-2fef-5a25-a712-143fa084435b', 'activity_name': 'pelletising of polyethylene, high density', 'reference_product_name': 'polyethylene, high density, pellets, recycled', 'product_information': "polyethylene, high density, pellets, recycled', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% recycled material. The product is used in the following applications and sectors: pellets ready for use in manufacturing of HDPE products (rigid plastics etc.)", 'source': 'Ecoinvent 3.11', 'geography': 'RER', 'similarity': 0.0}, {'activity_uuid': '1f64302f-2b55-5731-988b-2dde9451ae20', 'activity_name': 'polystyrene production, general purpose', 'reference_product_name': 'polystyrene, general purpose', 'product_information': "'polystyrene, general purpose', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: many applications such as food and non-food packaging, disposable cups and cutlery, furniture, toys and consumer goods, as well as electronics and appliances.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '836f454f-f07e-525f-81ed-25a3bcd5b02e', 'activity_name': 'phenolic resin production', 'reference_product_name': 'phenolic resin', 'product_information': "'phenolic resin', is a plastic product of fossil origin, it is not biodegradable and it is a thermoset material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: in the building industry as thermal insulation for flat roofs and as the core of structural sandwich elements for walls and roofing, preferred for insulation in transport (motor vehicles, railroad cars, ships) as well as in aeronauticand space applications.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '7ccfffe1-3d67-5e7c-994d-ac61accf0d15', 'activity_name': 'ethylene vinyl acetate copolymer production', 'reference_product_name': 'ethylene vinyl acetate copolymer', 'product_information': "'ethylene vinyl acetate copolymer', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: packaging and plastic goods industries, sealants in meat and dairy packaging structures, footwear, wire and cable insulation, pipes, toys, corks, photovoltaic encapsulation, medical packaging, hot melt adhesives, and lamination of glass to improve impact resistance.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'e3edabdd-bbcd-59c2-9efe-c63038009fda', 'activity_name': 'pelletising of polyethylene, low density, coloured', 'reference_product_name': 'polyethylene, low density, pellets, coloured, recycled', 'product_information': "polyethylene, low density, pellets, coloured, recycled', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% recycled material. The product is used in the following applications and sectors: mainly used as films, e.g. for the production of plastic bags, packaging material, and agricultural films. It is also used for electrical cable coating.", 'source': 'Ecoinvent 3.11', 'geography': 'RER', 'similarity': 0.0}, {'activity_uuid': 'f639917a-03cd-59fa-bf64-0e5a00064ae8', 'activity_name': 'polypropylene production, granulate', 'reference_product_name': 'polypropylene, granulate', 'product_information': "'polypropylene, granulate', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: one of the top three widely used polymers today with extremely wide range of applications as plastic or as fiber, either transparent or pigmented, such as food packaging, textiles, automotive components, furniture market, medical devices, and consumer goods.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'b3403798-a0e5-591d-8e0b-fb6ef018eec8', 'activity_name': 'polylactic acid production, granulate', 'reference_product_name': 'polylactic acid, granulate', 'product_information': "polylactic acid, granulate' is a plastic product of bio origin, it is biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: plastic films, bottles,  cups for cold drinks, stiff packages like clamshells as well as degradable films. It can be processed on standard equipment and used in injection moulding, injection stretch blow moulding, and for the production of blown and cast films, biaxially oriented films, or transformed to extrusion paper-coating compounds, etc.", 'source': 'Ecoinvent 3.11', 'geography': 'GLO', 'similarity': 0.0}, {'activity_uuid': '36334629-c058-59d2-a68b-7a320edf18d1', 'activity_name': 'polyvinylchloride, rigid, pellets, recycled to generic market for polyvinylchloride', 'reference_product_name': 'polyvinyl chloride, suspension polymerised', 'product_information': "'polyvinylchloride, suspension polymerised', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: Building products (window frames and other profiles, floor and wall coverings, roofing sheets, linings for tunnels, swimming-pools and reservoirs), piping (water and sewerage pipes and fittings, and ducts for power and telecommunications), coatings (tarpaulins, rainwear and corrugated metal sheets), insulation and sheathing for low voltage power supplies, telecommunications, appliances, and automotive applications, packaging, pharmaceuticals, food and confectionery, water and fruit juices, labels, presentation trays, automotive applications (cables, underbody coating and interior trimmings), medical products (blood bags, transfusion tubes and surgical gloves), leisure products (garden hoses, footwear, inflatable pools, tents).", 'source': 'Ecoinvent 3.11', 'geography': 'RER', 'similarity': 0.0}, {'activity_uuid': 'c5575ebe-28b9-582d-be12-24c8eb4b947e', 'activity_name': 'polymethyl methacrylate production', 'reference_product_name': 'polymethyl methacrylate', 'product_information': "'polymethyl methacrylate, sheet', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: light transmission, applications where lasting beautiful appearances are important, such as on furniture or kitchen or bath walls or cabinet facades, double-glazing improvements to increase the thermal insulation of windows in large buildings and greenhouses, ceiling construction.", 'source': 'Ecoinvent 3.11', 'geography': 'RAS', 'similarity': 0.0}, {'activity_uuid': 'dd66eaac-525f-548d-b9bf-a85aba10ea09', 'activity_name': 'polyvinylchloride, flexible, pellets, recycled to generic market for polyvinylchloride', 'reference_product_name': 'polyvinyl chloride, emulsion polymerised', 'product_information': "'polyvinylchloride, emulsion polymerised', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: Building products (window frames and other profiles, floor and wall coverings, roofing sheets, linings for tunnels, swimming-pools and reservoirs), piping (water and sewerage pipes and fittings, and ducts for power and telecommunications), coatings (tarpaulins, rainwear and corrugated metal sheets), insulation and sheathing for low voltage power supplies, telecommunications, appliances, and automotive applications, packaging, pharmaceuticals, food and confectionery, water and fruit juices, labels, presentation trays, automotive applications (cables, underbody coating and interior trimmings), medical products (blood bags, transfusion tubes and surgical gloves), leisure products (garden hoses, footwear, inflatable pools, tents).     ", 'source': 'Ecoinvent 3.11', 'geography': 'RER', 'similarity': 0.0}, {'activity_uuid': 'ff77aa48-13a2-5c36-bab0-83de3e01302b', 'activity_name': 'styrene-acrylonitrile copolymer production', 'reference_product_name': 'styrene-acrylonitrile copolymer', 'product_information': "'styrene-acrylonitrile copolymer', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: use in the kitchen as mixing bowls and basins and fittings for refrigerators, for outer casings of thermally insulated jugs, for tableware, cutlery, coffee filters, jars and beakers, storage containers for all kinds of foods, in multi-trip tableware for the catering sector.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'b831a82a-2dce-4062-8190-d6a2da09277f', 'activity_name': 'Polyethylene injection moulded hard plastic', 'reference_product_name': 'PE Hard plastic', 'product_information': 'Polyethylene injection moulded hard plastic', 'source': 'CarbonBright', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': 'f2c1f4fb-0706-5670-9558-89b1f039cbea', 'activity_name': 'chemical production, organic', 'reference_product_name': 'chemical, organic', 'product_information': "'chemical, organic' describes an unweighted average of 20 pure, organic substances, being part of the top 100 chemicals and included into this database. The mix contains mainly liquid chemicals. Modelling this substance in a solution requires the user to add the solvent of their choice in their models. The main application is as reagents and intermediates in producing other chemicals.", 'source': 'Ecoinvent 3.11', 'geography': 'GLO', 'similarity': 0.0}, {'activity_uuid': '1cc3f175-a16a-5074-99b8-79c22d6db5d8', 'activity_name': 'polystyrene production, expandable', 'reference_product_name': 'polystyrene, expandable', 'product_information': "'polystyrene, expandable', is a plastic product of fossil origin, it is not biodegradable and it is a thermoplastic material. This product consists of 100% virgin material with no content of recycled material. The product is used in the following applications and sectors: thermal insulation in buildings, road construction, sound insulation, packaging, food packaging to maintain the temperature of hot or cold food and prevent spoilage, protection for valuable and fragile goods, crash helmets, windsurfing boards", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}, {'activity_uuid': '9e8525a3-9ee0-591f-91ea-bfcd6251113e', 'activity_name': 'acrylic acid production, propylene oxidation', 'reference_product_name': 'acetic acid', 'product_information': "'acetic acid, without water, in 98% solution state' is an organic substance with a CAS no. : 000064-19-7. It is called 'acetic acid' under IUPAC naming and its molecular formula is: C2H4O2. It is liquid under normal conditions of temperature and pressure and may have a pungent odour. This dataset represents a pure substance (100% active substance). Industrially, this product is mostly used in 98% solution state. Modelling this substance in a solution requires the user to add the solvent of their choice in their models. On a consumer level, is used in the following products: coating products, washing & cleaning products, air care products, lubricants and greases, fillers, putties, plasters, modelling clay, anti-freeze products, fertilisers, plant protection products and finger paints. On industrial sites, the substance is used for the manufacture of products in the following sectors: laboratory chemicals, oil and gas exploration or production products, washing & cleaning products, water treatment chemicals, polymers, coating products and pH regulators and water treatment products.", 'source': 'Ecoinvent 3.11', 'geography': 'RoW', 'similarity': 0.0}]
.2025-05-28 11:35:10.370 | WARNING  | utils.cache:_get_all_keys:212 - _get_all_keys called on RedisCache - this operation can be expensive
2025-05-28 11:35:10.374 | INFO     | emissions_factor_matching.tests.test_api:setUp:23 - Cache clear response: 200
2025-05-28 11:35:10.378 | INFO     | emissions_factor_matching.api:get_recommended_activities:204 - API Version: latest
2025-05-28 11:35:10.378 | INFO     | emissions_factor_matching.api:get_recommended_activities:205 - Phase 1: Enhanced Input Category Prediction - Starting
2025-05-28 11:35:12.127 | INFO     | completions:get_structured_completion:119 - Structured completion successful: InputCategoryResponse
2025-05-28 11:35:12.128 | INFO     | emissions_factor_matching.api:get_recommended_activities:249 - Phase 1 Complete: Enhanced category = PRODUCT_CONSTRUCTION_MATERIAL
2025-05-28 11:35:12.130 | INFO     | emissions_factor_matching.api:get_recommended_activities:252 - Phase 2: Modifier Spotting - Starting
2025-05-28 11:35:13.932 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ModifiersResponse
2025-05-28 11:35:13.933 | INFO     | emissions_factor_matching.api:get_recommended_activities:254 - Phase 2 Complete: Extracted 3 modifiers = ['steel', 'raw', 'USA']
2025-05-28 11:35:13.938 | INFO     | emissions_factor_matching.api:get_recommended_activities:257 - Phase 3: ISIC Classification Mapping - Starting
2025-05-28 11:35:15.758 | INFO     | completions:get_structured_completion:119 - Structured completion successful: ISICClassificationResponse
2025-05-28 11:35:15.759 | INFO     | emissions_factor_matching.api:get_recommended_activities:259 - Phase 3 Complete: Mapped to 1 ISIC codes = ['2420']
2025-05-28 11:35:15.759 | INFO     | emissions_factor_matching.api:get_recommended_activities:262 - Phase 4: Query Text Augmentation - Starting
2025-05-28 11:35:18.267 | INFO     | completions:get_structured_completion:119 - Structured completion successful: AugmentedQueryResponse
2025-05-28 11:35:18.268 | INFO     | emissions_factor_matching.predictions:augment_query_text:763 - Query augmentation successful: 'Raw steel production processes in the USA, focusing on primary steel manufacturing from iron ore and scrap metal. Includes blast furnace and electric arc furnace methods, addressing raw material extraction, processing, and initial steel formation stages within the construction material sector. Relevant to ISIC 2420 for basic iron and steel production.'
2025-05-28 11:35:18.269 | INFO     | emissions_factor_matching.api:get_recommended_activities:264 - Phase 4 Complete: Augmented query = 'Raw steel production processes in the USA, focusing on primary steel manufacturing from iron ore and scrap metal. Includes blast furnace and electric arc furnace methods, addressing raw material extraction, processing, and initial steel formation stages within the construction material sector. Relevant to ISIC 2420 for basic iron and steel production.'
2025-05-28 11:35:18.270 | INFO     | emissions_factor_matching.api:get_recommended_activities:267 - Phase 5: Dynamic Filter Construction - Starting
2025-05-28 11:35:18.286 | INFO     | emissions_factor_matching.predictions:construct_dynamic_filters:880 - Constructed dynamic filters with 3 conditions
2025-05-28 11:35:18.287 | DEBUG    | emissions_factor_matching.predictions:construct_dynamic_filters:881 - Filter details: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '2420:Manufacture of basic precious and other non-ferrous metals'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}]}
2025-05-28 11:35:18.289 | INFO     | emissions_factor_matching.api:get_recommended_activities:269 - Phase 5 Complete: Dynamic filters = {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '2420:Manufacture of basic precious and other non-ferrous metals'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}]}
2025-05-28 11:35:19.979 | INFO     | completions:get_chat_completion:208 - Completion: NONE
2025-05-28 11:35:20.939 | INFO     | emissions_factor_matching.api:get_recommended_activities:275 - CAS Number: NONE
2025-05-28 11:35:20.940 | INFO     | emissions_factor_matching.api:get_recommended_activities:278 - Phase 6: ChromaDB Vector Search Enhancement - Starting
2025-05-28 11:35:20.941 | INFO     | emissions_factor_matching.dataset:search_candidates:72 - phase_6_search Executing ChromaDB vector search
2025-05-28 11:35:20.942 | INFO     | emissions_factor_matching.dataset:search_candidates:73 - phase_6_search Query: 'Raw steel production processes in the USA, focusing on primary steel manufacturing from iron ore and scrap metal. Includes blast furnace and electric arc furnace methods, addressing raw material extraction, processing, and initial steel formation stages within the construction material sector. Relevant to ISIC 2420 for basic iron and steel production.'
2025-05-28 11:35:20.943 | INFO     | emissions_factor_matching.dataset:search_candidates:74 - phase_6_search Filters: {'$and': [{'activity_type': {'$eq': 'ordinary transforming activity'}}, {'ISIC Classification': {'$eq': '2420:Manufacture of basic precious and other non-ferrous metals'}}, {'isic_section': {'$eq': 'C - Manufacturing'}}]}
2025-05-28 11:35:20.944 | INFO     | emissions_factor_matching.dataset:search_candidates:75 - phase_6_search Requesting 25 results
2025-05-28 11:35:22.869 | INFO     | emissions_factor_matching.dataset:search_candidates:97 - phase_6_search ChromaDB search completed in 1924.26ms
2025-05-28 11:35:22.870 | INFO     | emissions_factor_matching.dataset:search_candidates:98 - phase_6_search Retrieved 25 candidates
2025-05-28 11:35:22.872 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 1: 'aluminium, ingot, primary, import from unspecified' (distance: 0.2932, similarity: 0.7068)
2025-05-28 11:35:22.873 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 2: 'silicon production, metallurgical grade' (distance: 0.3094, similarity: 0.6906)
2025-05-28 11:35:22.874 | INFO     | emissions_factor_matching.dataset:search_candidates:156 - phase_6_search Candidate 3: 'aluminium oxide, metallurgical, import from Northern America' (distance: 0.3212, similarity: 0.6788)
2025-05-28 11:35:22.875 | INFO     | emissions_factor_matching.dataset:search_candidates:165 - phase_6_search Successfully created 25 candidate objects
2025-05-28 11:35:22.875 | INFO     | emissions_factor_matching.dataset:search_candidates:166 - phase_6_search Best match: 'aluminium, ingot, primary, import from unspecified' (distance: 0.2932)
2025-05-28 11:35:22.877 | INFO     | emissions_factor_matching.dataset:search_candidates_with_fallback:191 - phase_6_fallback Primary search successful with 25 candidates
2025-05-28 11:35:22.877 | INFO     | emissions_factor_matching.api:get_recommended_activities:284 - Phase 6 Complete: Retrieved 25 candidates
2025-05-28 11:35:22.883 | INFO     | emissions_factor_matching.api:get_recommended_activities:287 - Phase 7: LLM Re-ranking & Justification - Starting
2025-05-28 11:35:27.668 | INFO     | completions:get_structured_completion:119 - Structured completion successful: RerankingResponse
2025-05-28 11:35:27.670 | INFO     | emissions_factor_matching.predictions:re_rank_candidates:695 - Re-ranking complete: Selected 'aluminium, ingot, primary, import from unspecified' with LOW confidence
2025-05-28 11:35:27.672 | INFO     | emissions_factor_matching.api:get_recommended_activities:299 - Phase 7 Complete: Selected 'aluminium, ingot, primary, import from unspecified' with LOW confidence
2025-05-28 11:35:27.673 | INFO     | emissions_factor_matching.api:get_recommended_activities:302 - Phase 8: Geography Matching & Record Retrieval - Starting
E
======================================================================
ERROR: test_smoke_test_activity_recommendations (emissions_factor_matching.tests.test_api.TestAPI)
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/home/<USER>/app/emissions_factor_matching/tests/test_api.py", line 39, in test_smoke_test_activity_recommendations
    response = self.client.post(
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 633, in post
    return super().post(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 1145, in post
    return self.request(
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 516, in request
    return super().request(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 827, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 1015, in _send_single_request
    response = transport.handle_request(request)
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 398, in handle_request
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 395, in handle_request
    portal.call(self.app, scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/anyio/from_thread.py", line 288, in call
    return cast(T_Retval, self.start_task_soon(func, *args).result())
  File "/usr/local/lib/python3.10/concurrent/futures/_base.py", line 458, in result
    return self.__get_result()
  File "/usr/local/lib/python3.10/concurrent/futures/_base.py", line 403, in __get_result
    raise self._exception
  File "/usr/local/lib/python3.10/site-packages/anyio/from_thread.py", line 217, in _call_func
    retval = await retval_or_awaitable
  File "/usr/local/lib/python3.10/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/exceptions.py", line 65, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 756, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 776, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 297, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 77, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 72, in app
    response = await func(request)
  File "/usr/local/lib/python3.10/site-packages/fastapi/routing.py", line 278, in app
    raw_response = await run_endpoint_function(
  File "/usr/local/lib/python3.10/site-packages/fastapi/routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
  File "/home/<USER>/app/utils/cache.py", line 339, in wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/app/emissions_factor_matching/api.py", line 268, in get_recommended_activities
    dynamic_filters = construct_dynamic_filters(internal_request, enhanced_category, modifiers, isic_codes)
  File "/home/<USER>/app/emissions_factor_matching/predictions.py", line 816, in construct_dynamic_filters
    code = classification.split(':')[0] if ':' in classification else classification
TypeError: argument of type 'int' is not iterable

======================================================================
ERROR: test_smoke_test_activity_recommendations_with_geography (emissions_factor_matching.tests.test_api.TestAPI)
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/home/<USER>/app/emissions_factor_matching/tests/test_api.py", line 47, in test_smoke_test_activity_recommendations_with_geography
    response = self.client.post(
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 633, in post
    return super().post(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 1145, in post
    return self.request(
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 516, in request
    return super().request(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 827, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 1015, in _send_single_request
    response = transport.handle_request(request)
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 398, in handle_request
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 395, in handle_request
    portal.call(self.app, scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/anyio/from_thread.py", line 288, in call
    return cast(T_Retval, self.start_task_soon(func, *args).result())
  File "/usr/local/lib/python3.10/concurrent/futures/_base.py", line 458, in result
    return self.__get_result()
  File "/usr/local/lib/python3.10/concurrent/futures/_base.py", line 403, in __get_result
    raise self._exception
  File "/usr/local/lib/python3.10/site-packages/anyio/from_thread.py", line 217, in _call_func
    retval = await retval_or_awaitable
  File "/usr/local/lib/python3.10/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/exceptions.py", line 65, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 756, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 776, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 297, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 77, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 72, in app
    response = await func(request)
  File "/usr/local/lib/python3.10/site-packages/fastapi/routing.py", line 278, in app
    raw_response = await run_endpoint_function(
  File "/usr/local/lib/python3.10/site-packages/fastapi/routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
  File "/home/<USER>/app/utils/cache.py", line 339, in wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/app/emissions_factor_matching/api.py", line 268, in get_recommended_activities
    dynamic_filters = construct_dynamic_filters(internal_request, enhanced_category, modifiers, isic_codes)
  File "/home/<USER>/app/emissions_factor_matching/predictions.py", line 816, in construct_dynamic_filters
    code = classification.split(':')[0] if ':' in classification else classification
TypeError: argument of type 'int' is not iterable

======================================================================
ERROR: test_with_lca_lifecycle_stage (emissions_factor_matching.tests.test_api.TestAPI)
Test new lcaLifecycleStage parameter
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/home/<USER>/app/emissions_factor_matching/tests/test_api.py", line 98, in test_with_lca_lifecycle_stage
    response = self.client.post(
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 633, in post
    return super().post(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 1145, in post
    return self.request(
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 516, in request
    return super().request(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 827, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
  File "/usr/local/lib/python3.10/site-packages/httpx/_client.py", line 1015, in _send_single_request
    response = transport.handle_request(request)
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 398, in handle_request
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/testclient.py", line 395, in handle_request
    portal.call(self.app, scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/anyio/from_thread.py", line 288, in call
    return cast(T_Retval, self.start_task_soon(func, *args).result())
  File "/usr/local/lib/python3.10/concurrent/futures/_base.py", line 458, in result
    return self.__get_result()
  File "/usr/local/lib/python3.10/concurrent/futures/_base.py", line 403, in __get_result
    raise self._exception
  File "/usr/local/lib/python3.10/site-packages/anyio/from_thread.py", line 217, in _call_func
    retval = await retval_or_awaitable
  File "/usr/local/lib/python3.10/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/exceptions.py", line 65, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 756, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 776, in app
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 297, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 77, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 64, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 72, in app
    response = await func(request)
  File "/usr/local/lib/python3.10/site-packages/fastapi/routing.py", line 278, in app
    raw_response = await run_endpoint_function(
  File "/usr/local/lib/python3.10/site-packages/fastapi/routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
  File "/home/<USER>/app/utils/cache.py", line 339, in wrapper
    result = await func(*args, **kwargs)
  File "/home/<USER>/app/emissions_factor_matching/api.py", line 303, in get_recommended_activities
    matched_activity = get_activity_from_dataset(
  File "/home/<USER>/app/emissions_factor_matching/api.py", line 97, in get_activity_from_dataset
    activity = get_geography_activity_match(
  File "/home/<USER>/app/emissions_factor_matching/geography.py", line 130, in get_geography_activity_match
    geography_matches = get_geography_matches_with_priority(iso_code)
  File "/home/<USER>/app/emissions_factor_matching/geography.py", line 127, in get_geography_matches_with_priority
    return geography.get_priority_geographies()
AttributeError: 'NoneType' object has no attribute 'get_priority_geographies'

======================================================================
FAIL: test_backward_compatibility_chemical_name (emissions_factor_matching.tests.test_api.TestAPI)
Test that old parameter names still work
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/home/<USER>/app/emissions_factor_matching/tests/test_api.py", line 69, in test_backward_compatibility_chemical_name
    self.assertEqual(response.status_code, 200)
AssertionError: 422 != 200

----------------------------------------------------------------------
Ran 7 tests in 63.619s

FAILED (failures=1, errors=3)
load INSTRUCTOR_Transformer
max_seq_length  512
load INSTRUCTOR_Transformer
max_seq_length  512
Token has not been saved to git credential helper. Pass `add_to_git_credential=True` if you want to set the git credential as well.
Token is valid (permission: read).
Your token has been saved to /home/<USER>/.cache/huggingface/token
Login successful
