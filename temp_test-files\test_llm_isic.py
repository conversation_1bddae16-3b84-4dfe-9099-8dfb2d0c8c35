#!/usr/bin/env python3
"""
Test script for the new LLM-based ISIC classification
"""
import sys
sys.path.append('/home/<USER>/app')

from emissions_factor_matching.predictions import map_isic_classification
import logging

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_llm_isic():
    print("=== TESTING LLM-BASED ISIC CLASSIFICATION ===\n")
    
    test_cases = [
        {
            "name": "Repair of fabricated metal products",
            "enhanced_category": "SERVICE_REPAIR_METAL",
            "modifiers": ["fabricated", "metal", "maintenance"],
            "user_query": "repair of fabricated metal products",
            "expected_code": "3311"
        },
        {
            "name": "Road freight transport",
            "enhanced_category": "SERVICE_TRANSPORT_ROAD_FREIGHT", 
            "modifiers": ["diesel", ">32t", "freight"],
            "user_query": "freight truck transportation",
            "expected_code": "4923"
        },
        {
            "name": "Chemical manufacturing",
            "enhanced_category": "CHEMICAL_ORGANIC_SOLVENT",
            "modifiers": ["industrial grade", "organic"],
            "user_query": "organic chemical production",
            "expected_code": "2011"
        },
        {
            "name": "Electricity generation",
            "enhanced_category": "SERVICE_ENERGY_ELECTRICITY",
            "modifiers": ["renewable", "grid"],
            "user_query": "electricity power generation",
            "expected_code": "3510"
        },
        {
            "name": "Software development",
            "enhanced_category": "SERVICE_SOFTWARE_DEVELOPMENT",
            "modifiers": ["custom", "enterprise"],
            "user_query": "software development services",
            "expected_code": "6201"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. Testing: {test_case['name']}")
        print(f"   Category: {test_case['enhanced_category']}")
        print(f"   Modifiers: {test_case['modifiers']}")
        print(f"   User Query: {test_case['user_query']}")
        print(f"   Expected: {test_case['expected_code']}")
        
        try:
            result = map_isic_classification(
                test_case['enhanced_category'], 
                test_case['modifiers'],
                test_case['user_query']
            )
            print(f"   Result: {result}")
            
            if test_case['expected_code'] in result:
                print(f"   ✅ SUCCESS: Found expected code {test_case['expected_code']}!")
            elif result:
                print(f"   ⚠️  PARTIAL: Got codes {result}, but expected {test_case['expected_code']}")
            else:
                print(f"   ❌ FAILED: No codes returned")
                
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
        
        print()

def test_edge_cases():
    print("=== TESTING EDGE CASES ===\n")
    
    edge_cases = [
        {
            "name": "Empty query",
            "enhanced_category": "UNKNOWN_CATEGORY",
            "modifiers": [],
            "user_query": "",
        },
        {
            "name": "Very specific query",
            "enhanced_category": "PRODUCT_SPECIALIZED",
            "modifiers": ["high-tech", "precision"],
            "user_query": "precision manufacturing of semiconductor wafers",
        },
        {
            "name": "Ambiguous query",
            "enhanced_category": "SERVICE_GENERAL",
            "modifiers": ["business"],
            "user_query": "business services",
        }
    ]
    
    for i, test_case in enumerate(edge_cases, 1):
        print(f"{i}. Testing: {test_case['name']}")
        print(f"   Category: {test_case['enhanced_category']}")
        print(f"   Modifiers: {test_case['modifiers']}")
        print(f"   User Query: {test_case['user_query']}")
        
        try:
            result = map_isic_classification(
                test_case['enhanced_category'], 
                test_case['modifiers'],
                test_case['user_query']
            )
            print(f"   Result: {result}")
            print(f"   ✅ Handled gracefully")
                
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
        
        print()

if __name__ == "__main__":
    test_llm_isic()
    test_edge_cases()
