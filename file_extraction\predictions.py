from typing import List
from pydantic import BaseModel
from completions import get_chat_completion, get_structured_completion
from config import config


class StructuredFileContext(BaseModel):
    is_structured_table: bool
    weight_column: str | None
    weight_column_regex: str | None
    raw_material_names_columns: List[str]


class Weight(BaseModel):
    amount: float
    unit: str


class RawMaterial(BaseModel):
    raw_material: str
    manufacturing_method: str | None
    weight: Weight


class PackagingComponent(BaseModel):
    raw_material: str
    manufacturing_method: str | None
    component: str
    packaging_level: str
    weight: Weight


class BOMItems(BaseModel):
    raw_materials: List[RawMaterial]
    packaging_components: List[PackagingComponent]


class Location(BaseModel):
    city: str | None
    state_or_province: str | None
    country: str | None


class ProductInfo(BaseModel):
    product_name: str | None
    product_id: str | None
    factory_location: Location
    total_number_of_products: int


def predict_file_is_structured_table(file_context: str) -> StructuredFileContext:
    """
    Determine if a file contains a structured table with material and weight columns.
    Now uses structured output with Lang<PERSON>hai<PERSON>.
    """
    messages = [
        {
            "role": "system",
            "content": (
                "Provided the contents of a file determine whether it is a single well structured table with columns representing raw material/chemicals/ingredients and raw material/chemicals/ingredients weights.\n"
                "Respond with:\n"
                "- is_structured_table: whether it's a structured table\n"
                "- weight_column: weight column name\n"
                "- weight_column_regex: regular expression for extracting the weight from the weight column as a valid float (if column contains a range take the higher value)\n"
                "- raw_material_names_columns: name(s) of the raw material/chemicals/ingredients/material description column or that describe what the raw material is especially a short description (there can be multiple)"
            )
        },
        {
            "role": "user",
            "content": file_context
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=StructuredFileContext,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    return result if result else StructuredFileContext(
        is_structured_table=False,
        weight_column=None,
        weight_column_regex=None,
        raw_material_names_columns=[]
    )


def predict_file_section_relevance(file_section: str) -> bool:
    """
    Determine if a file section contains relevant BOM information.
    This returns a boolean, so no structured output needed.
    """
    messages = [
        {
            "role": "system",
            "content": (
                "Provided a section of a file BOM (bill-of-materials), determine whether the section contains relevant information about raw materials or packaging materials or weights.\n"
                "Output: TRUE | FALSE"
            )
        },
        {
            "role": "user",
            "content": file_section
        }
    ]

    completion = get_chat_completion(
        None,  # Client parameter is ignored in new implementation
        messages,
        temperature=0,
        deployment=config.azure_openai_deployment,
    )

    if not completion:
        return False

    return "true" in completion.strip().lower()


def get_packaging_level(file_section: str) -> str:
    """
    Extract packaging level information from a file section.
    Returns comma-separated values, so no structured output needed.
    """
    messages = [
        {
            "role": "system",
            "content": (
                "Provided the contents of a file extract all the different packaging levels in the file seperated by commas.\n"
                "Example: Primary,Secondary,Tertiary"
            )
        },
        {
            "role": "user",
            "content": file_section
        }
    ]

    completion = get_chat_completion(
        None,  # Client parameter is ignored in new implementation
        messages,
        temperature=0,
        deployment=config.azure_openai_deployment,
    )

    return completion if completion else ""


def get_bom_items_from_file(file_context: str) -> BOMItems:
    """
    Extract BOM (Bill of Materials) items from file content.
    Now uses structured output with LangChain.
    """
    messages = [
        {
            "role": "system",
            "content": (
                "Provided the contents of a file extract all the raw materials and their weights as well as the packaging components and their weights.\n"
                "Raw materials are materials that are used to create a product (eg. cotton, polyester, steel, plastic).\n"
                "Packaging components are materials that are used to package a product (eg. cardboard box, plastic bag, bubble wrap).\n"
                "Provide the response with:\n"
                "- raw_materials: list of raw materials with their manufacturing method (if available) and weight\n"
                "- packaging_components: list of packaging components with their component name, packaging level, and weight"
            )
        },
        {
            "role": "user",
            "content": file_context
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=BOMItems,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    return result if result else BOMItems(raw_materials=[], packaging_components=[])


def get_product_info_from_file(file_context: str) -> ProductInfo:
    """
    Extract product information from file content.
    Now uses structured output with LangChain.
    """
    messages = [
        {
            "role": "system",
            "content": (
                "Provided the contents of a file extract:\n"
                "- product_name: The name of the product\n"
                "- product_id: The product ID or SKU\n"
                "- factory_location: The location where the product is manufactured (city, state/province, country)\n"
                "- total_number_of_products: The total number of products (default to 1 if not specified)"
            )
        },
        {
            "role": "user",
            "content": file_context
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=ProductInfo,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    return result if result else ProductInfo(
        product_name=None,
        product_id=None,
        factory_location=Location(city=None, state_or_province=None, country=None),
        total_number_of_products=1
    )